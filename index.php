<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}
if ($_SESSION['tipo_usuario'] !== 'administrador') {
    header("Location: index-simples.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Página Inicial</title>
  <link rel="icon" type="image/png" href="assets/icone.PNG">
  <link rel="shortcut icon" href="assets/icone.PNG" type="image/png">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* Paleta de cores padronizada */
    :root {
      --primary-color: #6366f1;
      --primary-light: #a5b4fc;
      --primary-dark: #4f46e5;
      --secondary-color: #64748b;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;
      --info-color: #06b6d4;
      --light-bg: #f8fafc;
      --card-bg: #ffffff;
      --text-primary: #1e293b;
      --text-secondary: #64748b;
      --border-color: #e2e8f0;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
      --radius-sm: 6px;
      --radius-md: 8px;
      --radius-lg: 12px;
    }

    * {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Grid System para Dashboard */
    .dashboard-grid {
      display: grid !important;
      grid-template-columns: repeat(auto-fit, minmax(650px, 1fr)) !important;
      gap: 20px !important;
      margin-top: 24px;
      width: 100%;
    }

    @media (max-width: 1400px) {
      .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(550px, 1fr)) !important;
      }
    }

    @media (max-width: 768px) {
      .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-top: 20px;
      }
    }

    /* Cards modernos */
    .modern-card {
      background: var(--card-bg);
      border-radius: var(--radius-lg);
      overflow: hidden;
      transition: all 0.3s ease;
      position: relative;
      width: 100%;
      min-width: 0;
      max-width: none;
    }

    

    .card-header {
      padding: 16px 20px 12px;
    }

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .card-title i {
      font-size: 18px;
    }

    .card-content {
      padding: 20px;
    }

    /* Indicadores de métricas */
    .metric-indicator {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 20px;
    }

    .metric-icon {
      width: 48px;
      height: 48px;
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    .metric-icon.success { background: linear-gradient(135deg, var(--success-color), #059669); }
    .metric-icon.primary { background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); }
    .metric-icon.warning { background: linear-gradient(135deg, var(--warning-color), #d97706); }
    .metric-icon.info { background: linear-gradient(135deg, var(--info-color), #0891b2); }

    .metric-content h3 {
      font-size: 28px;
      font-weight: 700;
      margin: 0;
      color: var(--text-primary);
    }

    .metric-content p {
      font-size: 14px;
      color: var(--text-secondary);
      margin: 4px 0 0;
      font-weight: 500;
    }

    /* Tabelas modernas */
    .modern-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 12px;
      background: white;
      border-radius: var(--radius-md);
      overflow: hidden;
      box-shadow: var(--shadow-sm);
    }

    .modern-table thead {
      background: linear-gradient(135deg, var(--light-bg), #f1f5f9);
    }

    .modern-table th {
      padding: 13px 20px;
      text-align: left;
      font-weight: 600;
      color: var(--text-primary);
      font-size: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 1px solid var(--border-color);
    }

    .modern-table td {
      padding: 12px 15px;
      color: var(--text-primary);
      border-bottom: 1px solid var(--border-color);
      vertical-align: middle;
    }

    .modern-table tbody tr:hover {
      background: var(--light-bg);
      transition: background-color 0.2s ease;
    }

    .modern-table tbody tr:last-child td {
      border-bottom: none;
    }

    /* Gráficos modernos */
    .chart-container {
      position: relative;
      height: 240px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .chart-container canvas {
      max-height: 220px !important;
    }

    /* Ajuste específico para cards de entradas e saídas */
    

    .card-entradas .chart-container canvas,
    .card-saidas .chart-container canvas {
      max-height: 170px !important;
    }

    /* Ajuste das tabelas dos cards de entradas e saídas */
    .card-entradas .table-container,
    .card-saidas .table-container {
      max-height: 200px !important;
      padding-bottom: 15px !important;
    }

    .card-entradas .modern-table tbody tr:last-child td,
    .card-saidas .modern-table tbody tr:last-child td {
      padding-bottom: 15px !important;
    }

    /* Melhorar clareza dos textos dos gráficos de entradas e saídas */
    .card-entradas canvas,
    .card-saidas canvas {
      font-family: 'Inter', sans-serif !important;
    }

    /* Botões de ação modernos */
    .action-buttons {
      position: absolute;
      top: 20px;
      right: 24px;
      display: flex;
      gap: 10px;
      z-index: 10;
    }

    .btn-modern {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      background: white;
      color: var(--text-secondary);
      border-radius: var(--radius-sm);
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .btn-modern:hover {
      background: var(--light-bg);
      border-color: var(--primary-color);
      color: var(--primary-color);
      transform: translateY(-1px);
    }

    .btn-modern.success {
      background: var(--success-color);
      color: white;
      border-color: var(--success-color);
    }

    .btn-modern.success:hover {
      background: #059669;
      border-color: #059669;
    }

    .btn-modern.danger {
      background: var(--danger-color);
      color: white;
      border-color: var(--danger-color);
    }

    .btn-modern.danger:hover {
      background: #dc2626;
      border-color: #dc2626;
    }

    .btn-modern.info {
      background: var(--info-color);
      color: white;
      border-color: var(--info-color);
    }

    .btn-modern.info:hover {
      background: #0891b2;
      border-color: #0891b2;
    }

    /* Badges e status */
    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-badge.success {
      background: #dcfce7;
      color: #166534;
    }

    .status-badge.warning {
      background: #fef3c7;
      color: #92400e;
    }

    .status-badge.danger {
      background: #fee2e2;
      color: #991b1b;
    }

    .status-badge.info {
      background: #e0f2fe;
      color: #0c4a6e;
    }
    /* Responsividade aprimorada */
    @media (max-width: 1200px) {
      .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(500px, 1fr)) !important;
      }
    }

    @media (max-width: 768px) {
      .card-header {
        padding: 16px 20px 12px;
      }

      .card-content {
        padding: 20px;
      }

      .card-title {
        font-size: 16px;
      }

      .metric-content h3 {
        font-size: 24px;
      }

      .chart-container {
        height: 200px;
      }
    }

    /* Animações suaves */
    .modern-card, .btn-modern, .modern-table tbody tr {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Scrollbar personalizada para tabelas */
    .table-container {
      overflow-x: auto;
      border-radius: var(--radius-md);
    }

    .table-container::-webkit-scrollbar {
      height: 6px;
    }

    .table-container::-webkit-scrollbar-track {
      background: var(--light-bg);
      border-radius: 3px;
    }

    .table-container::-webkit-scrollbar-thumb {
      background: var(--border-color);
      border-radius: 3px;
    }

    .table-container::-webkit-scrollbar-thumb:hover {
      background: var(--text-secondary);
    }

    /* Forçar grid layout - CSS específico */
    .content-container .dashboard-grid {
      display: grid !important;
      grid-template-columns: repeat(auto-fit, minmax(650px, 1fr)) !important;
      gap: 20px !important;
      width: 100% !important;
      margin-top: 24px !important;
    }

    .content-container .dashboard-grid .modern-card {
      width: 100% !important;
      max-width: none !important;
      min-width: 0 !important;
      flex: none !important;
      display: block !important;
    }

    /* SOBRESCREVER GRID - Cards independentes não seguem grid */
    .independent-cards-area .modern-card {
      width: 100% !important;
      max-width: none !important;
      min-width: 0 !important;
      flex: none !important;
      display: block !important;
      position: relative !important;
    }

    /* Dropdown de Exportação */
    .export-dropdown {
      position: relative;
      display: inline-block;
    }

    .export-dropdown-btn {
      background: none;
      border: none;
      color: var(--text-secondary);
      font-size: 16px;
      cursor: pointer;
      padding: 8px;
      border-radius: var(--radius-sm);
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .export-dropdown-btn:hover {
      background: var(--light-bg);
      color: var(--text-primary);
    }

    .export-dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      background: white;
      min-width: 160px;
      box-shadow: var(--shadow-lg);
      border-radius: var(--radius-md);
      border: 1px solid var(--border-color);
      z-index: 1000;
      padding: 8px 0;
      margin-top: 4px;
    }

    .export-dropdown-content.show {
      display: block;
      animation: dropdownFadeIn 0.2s ease;
    }

    @keyframes dropdownFadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .export-dropdown-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      color: var(--text-primary);
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      border: none;
      background: none;
      width: 100%;
      text-align: left;
      transition: background-color 0.2s ease;
    }

    .export-dropdown-item:hover {
      background: var(--light-bg);
      color: var(--text-primary);
    }

    .export-dropdown-item i {
      font-size: 16px;
      width: 16px;
      text-align: center;
    }

    .export-dropdown-item.excel {
      color: var(--success-color);
    }

    .export-dropdown-item.pdf {
      color: var(--danger-color);
    }

    .export-dropdown-item.excel:hover {
      background: #dcfce7;
    }

    .export-dropdown-item.pdf:hover {
      background: #fee2e2;
    }

    /* Sistema de Tabela Lateral */
    .card-content-with-table {
      display: flex;
      transition: all 0.3s ease;
    }

    .chart-section {
      flex: 1;
      min-width: 0;
    }

    .table-section {
      width: 0;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      opacity: 0;
      padding-left: 0;
    }

    .table-section.show {
      width: 450px;
      opacity: 1;
      padding-left: 24px;
    }

    @media (max-width: 1400px) {
      .table-section.show {
        width: 400px;
      }
    }

    @media (max-width: 1200px) {
      .table-section.show {
        width: 350px;
      }
    }

    @media (max-width: 768px) {
      .card-content-with-table {
        flex-direction: column;
      }

      .table-section.show {
        width: 100%;
        padding-left: 0;
        padding-top: 24px;
        border-left: none;
        border-top: 1px solid var(--border-color);
      }
    }

    /* Botão de Toggle da Tabela */
    .table-toggle-btn {
      background: none;
      border: none;
      color: var(--text-secondary);
      font-size: 16px;
      cursor: pointer;
      padding: 8px;
      border-radius: var(--radius-sm);
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 5px;
      margin-left: 10px;
    }

    .table-toggle-btn:hover {
      background: var(--light-bg);
      color: var(--text-primary);
    }

    .table-toggle-btn.active {
      background: var(--primary-color);
      color: white;
    }

    .table-toggle-btn.active:hover {
      background: var(--primary-dark);
    }

    /* Animação do ícone */
    .table-toggle-btn i {
      transition: transform 0.2s ease;
    }

    .table-toggle-btn.active i {
      transform: scale(1.1);
    }

    /* Título da seção de tabela */
    .table-section-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .table-section-title i {
      color: var(--primary-color);
    }

    /* Layout de gráfico com legenda lateral */
    .chart-with-legend {
      display: flex;
      align-items: center;
      gap: 24px;
      margin: 16px 0;
    }

    .chart-container-pie {
      flex: 0 0 auto;
      width: 220px;
      height: 220px;
      position: relative;
    }

    .chart-legend-right {
      flex: 1;
      flex-direction: column;
      gap: 12px;
      padding-left: 20px;
      transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .chart-legend-right.hidden {
      opacity: 0;
      visibility: hidden;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 12px;
      border-radius: var(--radius-sm);
      transition: all 0.2s ease;
      cursor: pointer;
    }

    .legend-item:hover {
      background: var(--light-bg);
      transform: translateX(4px);
    }

    .legend-color {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      flex-shrink: 0;
      border: 2px solid #ffffff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .legend-text {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .legend-label {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
      line-height: 1.2;
    }

    .legend-value {
      font-size: 12px;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .legend-percentage {
      font-size: 11px;
      color: var(--primary-color);
      font-weight: 600;
    }

    @media (max-width: 768px) {
      .chart-with-legend {
        flex-direction: column;
        gap: 20px;
      }

      .chart-container-pie {
        width: 200px;
        height: 200px;
      }

      .chart-legend-right {
        padding-left: 0;
        width: 100%;
      }

      .legend-item {
        padding: 10px 12px;
      }
    }

    /* Botões de ação no topo do card */
    .card-actions-top {
      position: absolute;
      top: 16px;
      right: 20px;
      display: flex;
      align-items: center;
      gap: 8px;
      z-index: 5;
    }

    /* Ajustar posição relativa do card-content para os botões absolutos */
    .modern-card .card-content {
      position: relative;
    }

    /* Card de Solicitações Pendentes com padding reduzido - FORÇAR APLICAÇÃO */
    .cards-side-by-side .modern-card.card-solicitacoes-pendentes .card-content {
      padding: 8px !important;
      box-sizing: border-box !important;
    }

    /* Ajustar botões para o card de Solicitações Pendentes */
    .cards-side-by-side .card-solicitacoes-pendentes .card-actions-top {
      top: 10px !important;
      right: 10px !important;
    }

    /* Reduzir também margens internas se existirem */
    .cards-side-by-side .card-solicitacoes-pendentes .metric-indicator {
      margin-bottom: 8px !important;
    }

    /* CSS ULTRA ESPECÍFICO para garantir aplicação */
    div.cards-side-by-side div.modern-card.card-solicitacoes-pendentes div.card-content {
      padding: 6px !important;
      margin: 0 !important;
    }

    /* Reduzir espaçamento do grid interno também */
    .card-solicitacoes-pendentes div[style*="margin-top: 24px"] {
      margin-top: 12px !important;
    }

    /* ÁREA DE CARDS TOTALMENTE INDEPENDENTES - FORA DO GRID */
    .independent-cards-area {
      position: relative !important;
      width: 100% !important;
      min-height: auto !important;
      height: auto !important;
      margin-top: 20px !important;
      background: transparent !important;
      display: block !important;
      grid-template-columns: none !important;
      grid-template-rows: none !important;
      gap: 0 !important;
      grid-area: unset !important;
      overflow: visible !important;
      padding-bottom: 40px !important;
      flex: 1 !important;
    }

    /* Container principal sem interferência */
    .cards-independent-container {
      position: relative;
      width: 100%;
      min-height: 400px;
      margin-bottom: 20px;
    }

    /* CARDS TOTALMENTE INDEPENDENTES - POSICIONAMENTO LIVRE */

    /* Card Estoque - Posição e tamanho específicos */
    .independent-cards-area .card-container-estoque {
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      width: 680px !important;
      height: auto !important;
      z-index: 10 !important;
      background: transparent !important;
    }

    /* Card Solicitações - Posição e tamanho específicos */
    .independent-cards-area .card-container-solicitacoes {
      position: absolute !important;
      top: 0 !important;
      left: 690px !important;
      width: 280px !important;
      height: auto !important;
      z-index: 10 !important;
      background: transparent !important;
    }

    /* CSS para garantir que cada card seja totalmente independente */
    .cards-independent-container .modern-card {
      display: block !important;
      width: 100% !important;
      position: relative !important;
    }

    /* Garantir que containers não tenham interferência */
    .card-container-estoque,
    .card-container-solicitacoes,
    .card-container-estoque-minimo,
    .card-container-top-produtos,
    .card-container-validade,
    .card-container-entradas,
    .card-container-saidas,
    .card-container-comparativo,
    .card-container-cnpj,
    .card-container-gastos-setor,
    .card-container-valor-contrato,
    .card-container-epi {
      box-sizing: border-box !important;
      float: none !important;
      clear: none !important;
    }

    /* Sistema de containers com controle total de dimensões */
    .card-container-estoque {
      width: 100% !important;
      height: auto;
      display: block !important;
      grid-column: 1 !important;
    }

    .card-container-solicitacoes {
      width: 100% !important;
      height: auto;
      display: block !important;
      grid-column: 2 !important;
    }

    /* Card Estoque Mínimo - Posição e tamanho específicos */
    .independent-cards-area .card-container-estoque-minimo {
      position: absolute !important;
      top: 0 !important;
      left: 1010px !important;
      height: auto !important;
      z-index: 10 !important;
      background: transparent !important;
    }

    /* Card Comparativo - Posição e tamanho específicos */
    .independent-cards-area .card-container-comparativo {
      position: absolute !important;
      top: 350px !important;
      left: 0 !important;
      width: 800px !important;
      height: auto !important;
      z-index: 15 !important;
      background: transparent !important;
      display: block !important;
      float: none !important;
      grid-column: unset !important;
      grid-row: unset !important;
      margin: 0 !important;
      padding: 0 !important;
    }

    /* Card Top Produtos - Posição e tamanho específicos */
    .independent-cards-area .card-container-top-produtos {
      position: absolute !important;
      top: 1000px !important;
      left: 0 !important;
      width: 800px !important;
      height: auto !important;
      z-index: 10 !important;
      background: transparent !important;
    }

    /* Card Validade - Posição e tamanho específicos */
    .independent-cards-area .card-container-validade {
      position: absolute !important;
      top: 1050px !important;
      left: 0 !important;
      width: 750px !important;
      height: auto !important;
      z-index: 10 !important;
      background: transparent !important;
    }

    /* Card Entradas - Posição e tamanho específicos */
    .independent-cards-area .card-container-entradas {
      position: absolute !important;
      top: 1400px !important;
      left: 0 !important;
      width: 700px !important;
      height: auto !important;
      z-index: 10 !important;
      background: transparent !important;
    }

    .card-container-saidas {
      position: relative !important;
      width: 100% !important;
      height: auto !important;
      margin: 0 0 20px 0 !important;
      display: block !important;
      z-index: 1 !important;
    }

    .card-container-comparativo {
      position: relative !important;
      width: 100% !important;
      height: auto !important;
      margin: 0 0 20px 0 !important;
      display: block !important;
      z-index: 1 !important;
    }

    .card-container-cnpj {
      position: relative !important;
      width: 100% !important;
      height: auto !important;
      margin: 0 0 20px 0 !important;
      display: block !important;
      z-index: 1 !important;
    }

    .card-container-gastos-setor {
      position: relative !important;
      width: 100% !important;
      height: auto !important;
      margin: 0 0 20px 0 !important;
      display: block !important;
      z-index: 1 !important;
    }

    .card-container-valor-contrato {
      position: relative !important;
      width: 100% !important;
      height: auto !important;
      margin: 0 0 20px 0 !important;
      display: block !important;
      z-index: 1 !important;
    }

    .card-container-epi {
      position: relative !important;
      width: 100% !important;
      height: auto !important;
      margin: 20px 0 40px 0 !important;
      display: block !important;
      z-index: 1 !important;
      clear: both !important;
    }

    /* Estilos específicos para cada card com controle total de dimensões */
    .card-estoque {
      width: 100% !important;
      height: auto;
      min-height: 200px;
      box-sizing: border-box;
      display: block !important;
    }

    .card-estoque .card-content {
      padding: 12px;
      width: 100% !important;
      box-sizing: border-box;
    }

    .card-solicitacoes {
      width: 100% !important;
      height: auto;
      min-height: 150px;
      box-sizing: border-box;
      display: block !important;
    }

    .card-solicitacoes .card-content {
      padding: 8px;
      width: 100% !important;
      box-sizing: border-box;
    }

    .card-estoque-minimo {
      width: 100%;
      height: auto;
      min-height: 150px;
      box-sizing: border-box;
    }

    .card-estoque-minimo .card-content {
      padding: 10px;
      width: 100%;
      box-sizing: border-box;
    }

    /* Comportamento específico da tabela no card de estoque mínimo */
    .card-estoque-minimo .card-content-with-table {
      display: block !important;
      position: relative !important;
    }

    /* Mostrar chart-section mas ocultar apenas o gráfico */
    .card-estoque-minimo .chart-section {
      display: block !important;
    }

    .card-estoque-minimo .chart-section .metric-indicator {
      display: flex !important;
      align-items: center !important;
      gap: 15px !important;
      margin-bottom: 20px !important;
    }

    .card-estoque-minimo .metric-icon {
      flex-shrink: 0 !important;
    }

    .card-estoque-minimo .metric-content {
      display: flex !important;
      flex-direction: column !important;
    }

    .card-estoque-minimo .chart-section .chart-container {
      display: none !important;
    }

    .card-estoque-minimo .table-section {
      display: block !important;
      position: static !important;
      margin-top: 20px !important;
      background: transparent !important;
      padding: 0 !important;
      box-shadow: none !important;
      border-radius: 0 !important;
      opacity: 1 !important;
      visibility: visible !important;
    }

    /* Estilos da tabela sempre visível com altura fixa */
    .card-estoque-minimo {
      height: 340px !important;
      overflow: hidden !important;
    }

    .card-estoque-minimo .table-section {
      width: 100% !important;
      height: 280px !important;
      overflow: visible !important;
    }

    /* Estilos da tabela sempre visível com altura fixa */
    .card-estoque-minimo .table-container {
      width: 100% !important;
      max-width: none !important;
      height: 250px !important;
      overflow-y: auto !important;
      padding-bottom: 10px !important;
    }

    .card-estoque-minimo .modern-table {
      width: 100% !important;
      min-width: 500px !important;
    }

    /* Ocultar título da tabela */
    .card-estoque-minimo .table-section-title {
      display: none !important;
    }

    /* Ocultar botão de toggle da tabela no card de estoque mínimo */
    .card-estoque-minimo .table-toggle-btn {
      display: none !important;
    }

    /* Card de solicitações clicável */
    .card-solicitacoes {
      cursor: pointer !important;
    }

    /* Ocultar botão "Ver todas" no card de solicitações */
    .card-solicitacoes .card-actions-top {
      display: none !important;
    }

    /* Ajustar layout dos tipos de solicitações */
    .card-solicitacoes div[style*="grid-template-columns"] > div {
      display: flex !important;
      align-items: center !important;
      gap: 10px !important;
      
    }

    .card-solicitacoes div[style*="font-size: 20px"] {
      font-size: 28px !important;
      font-weight: 700 !important;
      min-width: 40px !important;
    }

    .card-solicitacoes div[style*="font-size: 12px"] {
      font-size: 14px !important;
      font-weight: 500 !important;
      margin-top: 0 !important;
    }

  

    /* Card de estoque com altura fixa */
    .card-estoque {
      height: 350px !important;
      overflow: hidden !important;
    }

    .card-estoque .card-content {
      height: 100% !important;
      overflow: hidden !important;
      display: flex !important;
      flex-direction: column !important;
    }

    /* Layout do card de estoque - gráfico ocupa toda a largura */
    .card-estoque .card-content-with-table {
      display: block !important;
      height: 100% !important;
      position: relative !important;
    }

    .card-estoque .chart-section {
      width: 100% !important;
      display: flex !important;
      flex-direction: column !important;
      justify-content: flex-start !important;
      margin-bottom: 0px !important;
    }

    /* Tabela como popup por cima dos descritivos */
    .card-estoque .table-section {
      position: absolute !important;
      top: 50px !important;
      left: 205px !important;
      width: 440px !important;
      max-height: 280px !important;
      overflow-y: auto !important;
      background: white !important;
      border-radius: 8px !important;
      padding: 10px !important;
      z-index: 100 !important;
      opacity: 0 !important;
      visibility: hidden !important;
      transform: translateY(-10px) !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    /* Tabela visível quando ativa */
    .card-estoque .table-section.show {
      opacity: 1 !important;
      visibility: visible !important;
      transform: translateY(0) !important;
    }

    /* Estilo da tabela popup */
    .card-estoque .table-container {
      max-height: 220px !important;
      overflow-y: auto !important;
      background: white !important;
      border-radius: 8px !important;
    }

    .card-estoque .modern-table {
      margin: 0 !important;
      background: white !important;
      border-radius: 8px !important;
      width: 100% !important;
    }

    .card-estoque .modern-table tbody tr:last-child td {
      border-bottom: none !important;
    }

    /* Título da tabela popup */
    .card-estoque .table-section-title {
      margin-bottom: 10px !important;
      padding-bottom: 8px !important;
      border-bottom: 2px solid #e2e8f0 !important;
      font-weight: 600 !important;
      color: var(--text-primary) !important;
    }



    /* Card Top 10 Produtos com altura menor e scroll fixo */
    .card-top-produtos {
      height: 350px !important;
      overflow: hidden !important;
    }

    .card-top-produtos .card-content {
      height: 100% !important;
      overflow: hidden !important;
    }

    .card-top-produtos .table-section {
      max-height: 200px !important;
      overflow-y: auto !important;
    }

    .card-top-produtos .chart-section {
      max-height: 200px !important;
      overflow: hidden !important;
    }

    .card-top-produtos {
      width: 100%;
      height: auto;
      box-sizing: border-box;
    }

    .card-top-produtos .card-content {
      padding: 15px;
      width: 100%;
      box-sizing: border-box;
    }

    .card-validade {
      width: 100%;
      height: auto;
      box-sizing: border-box;
    }

    .card-validade .card-content {
      padding: 15px;
      width: 100%;
      box-sizing: border-box;
    }

    .card-entradas {
      width: 100%;
      height: 350px;
      box-sizing: border-box;
      overflow: hidden;
    }

    .card-entradas .card-content {
      padding: 15px;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      overflow: hidden;
    }

    .card-saidas {
      width: 100%;
      height: 350px;
      box-sizing: border-box;
      overflow: hidden;
    }

    .card-saidas .card-content {
      padding: 15px;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      overflow: hidden;
    }

    .card-comparativo {
      width: 100% !important;
      height: auto !important;
      box-sizing: border-box !important;
      position: relative !important;
      display: block !important;
      float: none !important;
      margin: 0 !important;
      padding: 0 !important;
      grid-column: unset !important;
      grid-row: unset !important;
    }

    .card-comparativo .card-content {
      padding: 15px;
      width: 100%;
      box-sizing: border-box;
    }

    .card-cnpj {
      width: 100%;
      height: auto;
      box-sizing: border-box;
    }

    .card-cnpj .card-content {
      padding: 15px;
      width: 100%;
      box-sizing: border-box;
    }

    .card-gastos-setor {
      width: 100%;
      height: auto;
      box-sizing: border-box;
    }

    .card-gastos-setor .card-content {
      padding: 15px;
      width: 100%;
      box-sizing: border-box;
    }

    .card-valor-contrato {
      width: 100%;
      height: auto;
      box-sizing: border-box;
    }

    .card-valor-contrato .card-content {
      padding: 15px;
      width: 100%;
      box-sizing: border-box;
    }

    .card-epi {
      width: 100%;
      height: auto;
      box-sizing: border-box;
    }

    .card-epi .card-content {
      padding: 15px;
      width: 100%;
      box-sizing: border-box;
    }

    /* Responsividade para mobile */
    @media (max-width: 768px) {
      .card-container-estoque,
      .card-container-solicitacoes,
      .card-container-estoque-minimo {
        width: 100% !important;
        margin-left: 0 !important;
        margin-bottom: 15px !important;
        display: block !important;
      }
    }

    /* Ajustar tabela para ficar abaixo dos botões */
    .table-section {
      padding-top: 30px; /* Espaço para os botões */
    }

    .table-section .table-section-title {
      margin-top: -30px; /* Ajustar título para posição correta */
    }
    /* Aumentar tamanho do gráfico de pizza no card de estoque */
    .card-estoque .chart-container-pie {
      width: 200px !important;
      height: 200px !important;
    }

    /* Ajustar o container do gráfico com legenda - otimizado para 700px */
    .card-estoque .chart-with-legend {
      gap: 15px;
      align-items: flex-start !important;
      margin-top: 5px !important;
      margin-bottom: 0px !important;
      justify-content: center !important;
      position: relative !important;
    }

    /* Adicionar margens e ajustar padding do card de estoque */
    .card-estoque .card-content {
      padding: 12px !important;
      
      margin-right: 20px !important;
    }

    /* Ajustar tamanho da fonte do indicador métrico */
    .card-estoque .metric-content h3 {
      font-size: 22px !important;
    }

    .card-estoque .metric-content p {
      font-size: 12px !important;
    }



    /* Ajustar legenda para melhor visualização com scroll invisível */
    .card-estoque .chart-legend-right {
      padding-left: 35px !important;
      max-height: 180px !important;
      
      overflow-y: auto !important;
      padding-right: 10px !important;
      margin-bottom: 0px !important;
    }

    /* Barra de rolagem invisível para a legenda */
    .card-estoque .chart-legend-right::-webkit-scrollbar {
      width: 0px !important;
      background: transparent !important;
    }

    .card-estoque .legend-item {
      padding: 5px 8px !important;
      margin-bottom: 3px !important;
    }

    .card-estoque .legend-label {
      font-size: 13px !important;
    }

    .card-estoque .legend-value {
      font-size: 12px !important;
    }

    .card-estoque .legend-percentage {
      font-size: 11px !important;
    }

    .card-estoque .legend-color {
      width: 14px !important;
      height: 14px !important;
    }

    /* Ajustar posição dos botões de ação no card de estoque */
    .card-estoque .card-actions-top {
      top: 8px !important;
      right: 70px !important;
      z-index: 20 !important;
    }

    /* Garantir que o gráfico não seja cortado pelos botões */
    .card-estoque .chart-with-legend {
      padding-top: 0 !important;
      margin-top: 0px !important;
      margin-bottom: 0px !important;
    }

    /* Ajustar posicionamento do indicador métrico */
    .card-estoque .metric-indicator {
      margin-bottom: 35px !important;
    }

    /* Ajustar container do gráfico para melhor posicionamento */
    .card-estoque .chart-container-pie canvas {
      max-width: 200px !important;
      max-height: 200px !important;
    }

    /* Garantir que o conteúdo fique dentro do card */
    .card-estoque .card-content-with-table {
      width: 100% !important;
      max-width: 100% !important;
      overflow: hidden !important;
      box-sizing: border-box !important;
    }



    /* Melhorar barra de rolagem da tabela no card de estoque */
    .card-estoque .table-container::-webkit-scrollbar {
      width: 8px !important;
      height: 8px !important;
    }

    .card-estoque .table-container::-webkit-scrollbar-track {
      background: #f1f5f9 !important;
      border-radius: 4px !important;
    }

    .card-estoque .table-container::-webkit-scrollbar-thumb {
      background: #cbd5e1 !important;
      border-radius: 4px !important;
      border: 1px solid #e2e8f0 !important;
    }

    .card-estoque .table-container::-webkit-scrollbar-thumb:hover {
      background: #94a3b8 !important;
    }

    .card-estoque .table-container::-webkit-scrollbar-corner {
      background: #f1f5f9 !important;
    }

    /* Container para Cards de Entradas e Saídas */
    .cards-entradas-saidas-container {
      position: relative !important;
      width: 100% !important;
      display: flex !important; 
      margin: 5px 840px !important;
      flex-wrap: wrap !important;
    }

    /* Cards de Entradas e Saídas - Independentes e posicionáveis */
    .cards-entradas-saidas-container .modern-card {
      position: relative !important;
      flex: 1 !important;
      min-width: 710px !important;
      max-width: calc(50% - 10px) !important;
      margin: 0 !important;
      box-sizing: border-box !important;
      height: 320px !important;
      overflow: hidden !important;
    }

    /* Permitir posicionamento customizado via CSS inline */
    .cards-entradas-saidas-container .modern-card[style*="top"],
    .cards-entradas-saidas-container .modern-card[style*="left"],
    .cards-entradas-saidas-container .modern-card[style*="margin-top"],
    .cards-entradas-saidas-container .modern-card[style*="margin-left"] {
      position: relative !important;
    }

    /* Responsividade para mobile */
    @media (max-width: 768px) {
      .cards-entradas-saidas-container {
        flex-direction: column !important;
        gap: 15px !important;
      }

      .cards-entradas-saidas-container .modern-card {
        max-width: 100% !important;
        min-width: 100% !important;
      }
    }

    /* Container para Cards de Validade e Top Produtos */
    .cards-validade-top-produtos-container {
      position: relative !important;
      width: 100% !important;
      display: flex !important;
      gap: 20px !important;
      margin: 20px 0 !important;
      
    }

    /* Cards de Validade e Top Produtos - Independentes e posicionáveis */
    .cards-validade-top-produtos-container .modern-card {
      position: relative !important;
      flex: 1 !important;
      min-width: 772px !important;
      max-width: calc(50% - 10px) !important;
      margin: 0 !important;
      box-sizing: border-box !important;
    }

    /* Altura específica para o card de validade */
    .cards-validade-top-produtos-container .card-validade {
      height: 350px !important;
      overflow: hidden !important;
    }

    .cards-validade-top-produtos-container .card-validade .card-content {
      height: calc(100% - 60px) !important;
      overflow: hidden !important;
    }

    .cards-validade-top-produtos-container .card-validade .chart-container {
      height: 180px !important;
    }

    .cards-validade-top-produtos-container .card-validade .table-container {
      max-height: 150px !important;
    }

    /* Permitir posicionamento customizado via CSS inline */
    .cards-validade-top-produtos-container .modern-card[style*="top"],
    .cards-validade-top-produtos-container .modern-card[style*="left"],
    .cards-validade-top-produtos-container .modern-card[style*="margin-top"],
    .cards-validade-top-produtos-container .modern-card[style*="margin-left"] {
      position: relative !important;
    }

    /* Responsividade para mobile */
    @media (max-width: 768px) {
      .cards-validade-top-produtos-container {
        flex-direction: column !important;
        gap: 15px !important;
      }

      .cards-validade-top-produtos-container .modern-card {
        max-width: 100% !important;
        min-width: 100% !important;
      }
    }

    /* Container para Cards de CNPJ, Gastos por Setor e Contratos */
    .cards-cnpj-gastos-contratos-container {
      position: relative !important;
      width: 100% !important;
      display: flex !important;
      gap: 20px !important;
      margin: 20px 0 !important;
      
    }

    /* Cards de CNPJ, Gastos e Contratos - Lado a lado com mesma altura */
    .cards-cnpj-gastos-contratos-container .modern-card {
      position: relative !important;
      flex: 1 !important;
      min-width: 508px !important;
      max-width: calc(33.333% - 14px) !important;
      margin: 0 !important;
      box-sizing: border-box !important;
      height: 500px !important;
      overflow: hidden !important;
    }

    .cards-cnpj-gastos-contratos-container .modern-card .card-content {
      height: calc(100% - 60px) !important;
      overflow: hidden !important;
    }

    .cards-cnpj-gastos-contratos-container .modern-card .chart-container {
      height: 220px !important;
    }

    .cards-cnpj-gastos-contratos-container .modern-card .table-container {
      max-height: 200px !important;
    }

    /* Responsividade para mobile */
    @media (max-width: 1200px) {
      .cards-cnpj-gastos-contratos-container {
        flex-direction: column !important;
        gap: 15px !important;
      }

      .cards-cnpj-gastos-contratos-container .modern-card {
        max-width: 100% !important;
        min-width: 100% !important;
        height: auto !important;
      }
    }
  </style>
</head>
<body style="background: var(--light-bg); margin: 0; padding: 0; min-height: 100vh;">
<?php include 'topbar.php'; ?>
<div class="content-container">
  <div class="page-header">
    <h1 class="page-title">Dashboard</h1>
    <p class="page-subtitle">Visão geral do sistema de controle de EPIs</p>
  </div>

  <!-- Grid de Cards do Dashboard -->
  <div class="dashboard-grid">
    <!-- Grid vazio - cards agora são independentes -->
  </div>

  <!-- Área de Cards Independentes -->
  <div class="independent-cards-area">

</body>
<style>
.content-container {
  background: var(--card-bg);
  border-radius: 20px;
  margin: 20px 20px 20px 290px;
  padding: 32px 32px 1800px 32px;
  min-height: calc(100vh - 40px);
  height: auto;
  box-shadow: var(--shadow-lg);
  position: relative;
  z-index: 1;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
}

@media (max-width: 768px) {
  .content-container {
    margin: 10px;
    border-radius: 15px;
    padding: 24px;
  }

  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 14px;
  }
}
</style>
<script>
    function toggleMenu(menuId) {
      const menu = document.getElementById(menuId);
      menu.style.display = menu.style.display === "block" ? "none" : "block";
    }
  </script>

<?php
// Bloco: Estoque Geral - Total de Itens no Estoque Atual
include 'conexao.php';

// Consulta para obter totais por categoria
$sql = "SELECT
    COALESCE(NULLIF(categoria, ''), 'Sem categoria') as categoria,
    COUNT(*) as total_produtos,
    SUM(quantidade) as quantidade_total
FROM produtos
WHERE status = 'ativo'
GROUP BY COALESCE(NULLIF(categoria, ''), 'Sem categoria')
ORDER BY quantidade_total DESC";
$result = $conn->query($sql);

$categorias = [];
$totalGeral = 0;
while ($row = $result->fetch_assoc()) {
    $categorias[] = $row;
    $totalGeral += $row['quantidade_total'];
}

// Top 10 Produtos Mais Estocados
$sqlTopEstocados = "SELECT nome, quantidade, categoria FROM produtos WHERE status = 'ativo' ORDER BY quantidade DESC LIMIT 10";
$resultTopEstocados = $conn->query($sqlTopEstocados);
$topEstocados = [];
while ($row = $resultTopEstocados->fetch_assoc()) {
    $topEstocados[] = $row;
}

// Produtos com Estoque Mínimo Atingido (Semáforo)
$sqlEstMin = "SELECT nome, quantidade, estoque_minimo FROM produtos WHERE status = 'ativo'";
$resultEstMin = $conn->query($sqlEstMin);
$produtosEstMin = [];
while ($row = $resultEstMin->fetch_assoc()) {
    if ($row['quantidade'] <= 0) {
        $row['status'] = 'zerado';
    } elseif ($row['quantidade'] <= $row['estoque_minimo']) {
        $row['status'] = 'abaixo';
    } else {
        $row['status'] = 'ok';
    }
    $produtosEstMin[] = $row;
}
// Ordenar por criticidade: zerado > abaixo > ok
usort($produtosEstMin, function($a, $b) {
    $ordem = ['zerado' => 0, 'abaixo' => 1, 'ok' => 2];
    return $ordem[$a['status']] <=> $ordem[$b['status']];
});
// Valor padrão para exibição
$limiteEstMin = isset($_GET['limiteEstMin']) ? $_GET['limiteEstMin'] : 10;
$produtosEstMinExibir = ($limiteEstMin === 'todos') ? $produtosEstMin : array_slice($produtosEstMin, 0, (int)$limiteEstMin);

// Contar apenas produtos zerados ou abaixo do estoque mínimo
$produtosCriticos = array_filter($produtosEstMin, function($produto) {
    return $produto['status'] === 'zerado' || $produto['status'] === 'abaixo';
});
$totalProdutosCriticos = count($produtosCriticos);

// Produtos com Validade Próxima (até 30 dias)
$sqlValidade = "SELECT nome, validade, quantidade FROM produtos WHERE status = 'ativo' AND validade IS NOT NULL AND validade != '0000-00-00' ORDER BY validade ASC";
$resultValidade = $conn->query($sqlValidade);
$produtosValidade = [];
$hoje = new DateTime();
while ($row = $resultValidade->fetch_assoc()) {
    $dataVal = new DateTime($row['validade']);
    $diasRestantes = (int)$hoje->diff($dataVal)->format('%r%a');
    $row['dias_restantes'] = $diasRestantes;
    if ($diasRestantes < 0) {
        $row['status'] = 'vencido';
    } elseif ($diasRestantes <= 30) {
        $row['status'] = 'proximo';
    } else {
        $row['status'] = 'ok';
    }
    $produtosValidade[] = $row;
}
$limiteValidade = isset($_GET['limiteValidade']) ? $_GET['limiteValidade'] : 10;
$produtosValidadeExibir = ($limiteValidade === 'todos') ? $produtosValidade : array_slice($produtosValidade, 0, (int)$limiteValidade);

// ================= ENTRADAS POR MÊS (ÚLTIMOS 12 MESES) =================
// Buscar os últimos 12 meses
$meses = [];
for ($i = 11; $i >= 0; $i--) {
    $mes = date('Y-m', strtotime("-{$i} months"));
    $meses[$mes] = [
        'mes' => $mes,
        'total_entradas' => 0,
        'quantidade' => 0,
        'valor_total' => 0.0
    ];
}
// Buscar entradas dos últimos 12 meses
$sqlEntradas = "SELECT id, data_hora FROM entradas_estoque WHERE data_hora >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 11 MONTH), '%Y-%m-01') AND (status IS NULL OR status = 'ativa')";
$resultEntradas = $conn->query($sqlEntradas);
$entradasPorMes = [];
$entradaIdsPorMes = [];
while ($row = $resultEntradas->fetch_assoc()) {
    $mes = date('Y-m', strtotime($row['data_hora']));
    if (isset($meses[$mes])) {
        $meses[$mes]['total_entradas']++;
        $entradaIdsPorMes[$mes][] = $row['id'];
    }
}
// Buscar quantidades e valores por entrada
foreach ($entradaIdsPorMes as $mes => $ids) {
    if (empty($ids)) continue;
    $idsStr = implode(',', $ids);
    // Buscar produtos das entradas deste mês
    $sql = "SELECT pe.quantidade, p.valor FROM produtos_entrada pe JOIN produtos p ON pe.codigo = p.codigo WHERE pe.entrada_id IN ($idsStr)";
    $res = $conn->query($sql);
    while ($r = $res->fetch_assoc()) {
        $meses[$mes]['quantidade'] += (int)$r['quantidade'];
        $meses[$mes]['valor_total'] += ((float)$r['quantidade']) * ((float)$r['valor']);
    }
}
// Preparar dados para gráfico e tabela
$labelsEntradas = [];
$dataEntradas = [];
$dataQuantidades = [];
$dataValores = [];
foreach ($meses as $m) {
    $labelsEntradas[] = date('m/Y', strtotime($m['mes'].'-01'));
    $dataEntradas[] = $m['total_entradas'];
    $dataQuantidades[] = $m['quantidade'];
    $dataValores[] = round($m['valor_total'], 2);
}

// Descobrir o nome da coluna de data em cada tabela para usar como data_solicitacao
$data_requisicoes = "NOW() as data_solicitacao";
$data_pedidos_mensais = "NOW() as data_solicitacao";
$data_pedidos_especiais = "NOW() as data_solicitacao";
$status_requisicoes = "'pendente' as status";
$status_pedidos_mensais = "'pendente' as status";
$status_pedidos_especiais = "'pendente' as status";
$col_data_requisicoes = null;
$col_data_pedidos_mensais = null;
$col_data_pedidos_especiais = null;
$check_requisicoes_data = $conn->query("SHOW COLUMNS FROM requisicoes LIKE '%data%'");
if ($check_requisicoes_data && $check_requisicoes_data->num_rows > 0) {
    $col = $check_requisicoes_data->fetch_assoc();
    $data_requisicoes = $col['Field'] . " as data_solicitacao";
    $col_data_requisicoes = $col['Field'];
}
$check_pedidos_mensais_data = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE '%data%'");
if ($check_pedidos_mensais_data && $check_pedidos_mensais_data->num_rows > 0) {
    $col = $check_pedidos_mensais_data->fetch_assoc();
    $data_pedidos_mensais = $col['Field'] . " as data_solicitacao";
    $col_data_pedidos_mensais = $col['Field'];
}
$check_pedidos_especiais_data = $conn->query("SHOW COLUMNS FROM pedidos_especiais LIKE '%data%'");
if ($check_pedidos_especiais_data && $check_pedidos_especiais_data->num_rows > 0) {
    $col = $check_pedidos_especiais_data->fetch_assoc();
    $data_pedidos_especiais = $col['Field'] . " as data_solicitacao";
    $col_data_pedidos_especiais = $col['Field'];
}
$check_requisicoes_status = $conn->query("SHOW COLUMNS FROM requisicoes WHERE Field = 'status'");
if ($check_requisicoes_status && $check_requisicoes_status->num_rows > 0) {
    $status_requisicoes = "status";
}
$check_pedidos_mensais_status = $conn->query("SHOW COLUMNS FROM pedidos_mensais WHERE Field = 'status'");
if ($check_pedidos_mensais_status && $check_pedidos_mensais_status->num_rows > 0) {
    $status_pedidos_mensais = "status";
}
$check_pedidos_especiais_status = $conn->query("SHOW COLUMNS FROM pedidos_especiais WHERE Field = 'status'");
if ($check_pedidos_especiais_status && $check_pedidos_especiais_status->num_rows > 0) {
    $status_pedidos_especiais = "status";
}
$sql_requisicoes = "SELECT codigo_solicitacao as codigo, 'requisicao' as tipo, $data_requisicoes, $status_requisicoes FROM requisicoes";
$sql_pedidos_mensais = "SELECT codigo_pedido as codigo, 'pedido_mensal' as tipo, $data_pedidos_mensais, $status_pedidos_mensais FROM pedidos_mensais";
$sql_pedidos_especiais = "SELECT codigo_pedido as codigo, 'pedido_especial' as tipo, $data_pedidos_especiais, $status_pedidos_especiais FROM pedidos_especiais";
$sqlSolicPend = "($sql_requisicoes) UNION ($sql_pedidos_mensais) UNION ($sql_pedidos_especiais) ORDER BY data_solicitacao DESC";
$resSolicPend = $conn->query($sqlSolicPend);
$pendentesTotal = 0;
$pendentesPorTipo = [
  'requisicao' => 0,
  'pedido_mensal' => 0,
  'pedido_especial' => 0
];
if ($resSolicPend) {
  while ($row = $resSolicPend->fetch_assoc()) {
    if (isset($row['status']) && $row['status'] == 'concluido') continue;
    $pendentesTotal++;
    if (isset($pendentesPorTipo[$row['tipo']])) {
      $pendentesPorTipo[$row['tipo']]++;
    }
  }
}
// ================= SAÍDAS POR MÊS (ÚLTIMOS 12 MESES) =================
$mesesSaidas = [];
for ($i = 11; $i >= 0; $i--) {
    $mes = date('Y-m', strtotime("-{$i} months"));
    $mesesSaidas[$mes] = [
        'mes' => $mes,
        'total_saidas' => 0,
        'quantidade' => 0,
        'valor_total' => 0.0
    ];
}
// Buscar solicitações concluídas (requisicoes e pedidos mensais)
$sqlSolicitacoesConcluidas = "
    (SELECT codigo_solicitacao as codigo, 'requisicao' as tipo, $data_requisicoes, status FROM requisicoes WHERE status = 'concluido')
    UNION ALL
    (SELECT codigo_pedido as codigo, 'pedido_mensal' as tipo, $data_pedidos_mensais, status FROM pedidos_mensais WHERE status = 'concluido')
";
$resultSolicitacoesConcluidas = $conn->query($sqlSolicitacoesConcluidas);
while ($row = $resultSolicitacoesConcluidas->fetch_assoc()) {
    $mes = date('Y-m', strtotime($row['data_solicitacao']));
    if (!isset($mesesSaidas[$mes])) continue;
    if ($row['tipo'] === 'requisicao') {
        $sqlItens = "SELECT i.quantidade, p.valor FROM itens_solicitacao i JOIN produtos p ON i.produto = p.codigo WHERE i.codigo_solicitacao = ?";
        $stmtItens = $conn->prepare($sqlItens);
        $stmtItens->bind_param('i', $row['codigo']);
        $stmtItens->execute();
        $resItens = $stmtItens->get_result();
        $qtd = 0;
        $valor = 0.0;
        while ($item = $resItens->fetch_assoc()) {
            $qtd += (int)$item['quantidade'];
            $valor += ((float)$item['quantidade']) * ((float)$item['valor']);
        }
    } else if ($row['tipo'] === 'pedido_mensal') {
        $sqlItens = "SELECT i.quantidade, p.valor FROM itens_pedido_mensal i JOIN produtos p ON i.produto = p.codigo WHERE i.codigo_pedido = ?";
        $stmtItens = $conn->prepare($sqlItens);
        $stmtItens->bind_param('i', $row['codigo']);
        $stmtItens->execute();
        $resItens = $stmtItens->get_result();
        $qtd = 0;
        $valor = 0.0;
        while ($item = $resItens->fetch_assoc()) {
            $qtd += (int)$item['quantidade'];
            $valor += ((float)$item['quantidade']) * ((float)$item['valor']);
        }
    } else {
        continue;
    }
    $mesesSaidas[$mes]['total_saidas']++;
    $mesesSaidas[$mes]['quantidade'] += $qtd;
    $mesesSaidas[$mes]['valor_total'] += $valor;
}
// Também somar as saídas registradas diretamente em saidas_estoque
$sqlSaidasEstoque = "SELECT id, data_saida FROM saidas_estoque WHERE data_saida >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 11 MONTH), '%Y-%m-01') AND (status IS NULL OR status = 'ativa')";
$resultSaidasEstoque = $conn->query($sqlSaidasEstoque);
$saidaIdsPorMes = [];
while ($row = $resultSaidasEstoque->fetch_assoc()) {
    $mes = date('Y-m', strtotime($row['data_saida']));
    if (isset($mesesSaidas[$mes])) {
        $saidaIdsPorMes[$mes][] = $row['id'];
        $mesesSaidas[$mes]['total_saidas']++;
    }
}
// Buscar quantidades e valores por saída
foreach ($saidaIdsPorMes as $mes => $ids) {
    if (empty($ids)) continue;
    $idsStr = implode(',', $ids);
    $sql = "SELECT ps.quantidade, p.valor FROM produtos_saida ps JOIN produtos p ON ps.codigo = p.codigo WHERE ps.saida_id IN ($idsStr)";
    $res = $conn->query($sql);
    while ($r = $res->fetch_assoc()) {
        $mesesSaidas[$mes]['quantidade'] += (int)$r['quantidade'];
        $mesesSaidas[$mes]['valor_total'] += ((float)$r['quantidade']) * ((float)$r['valor']);
    }
}
$labelsSaidas = [];
$dataSaidas = [];
$dataQuantidadesSaidas = [];
$dataValoresSaidas = [];
foreach ($mesesSaidas as $m) {
    $labelsSaidas[] = date('m/Y', strtotime($m['mes'].'-01'));
    $dataSaidas[] = $m['total_saidas'];
    $dataQuantidadesSaidas[] = $m['quantidade'];
    $dataValoresSaidas[] = round($m['valor_total'], 2);
}

// ================= COMPARATIVO ENTRADAS VS SAÍDAS (ÚLTIMOS 6 MESES) =================
$mesesComp = [];
for ($i = 5; $i >= 0; $i--) {
    $mes = date('Y-m', strtotime("-{$i} months"));
    if (strtotime($mes.'-01') <= strtotime(date('Y-m-01'))) {
        $mesesComp[$mes] = [
            'mes' => $mes,
            'entradas' => isset($meses[$mes]) ? $meses[$mes]['quantidade'] : 0,
            'saidas' => isset($mesesSaidas[$mes]) ? $mesesSaidas[$mes]['quantidade'] : 0,
            'diferenca' => (isset($meses[$mes]) ? $meses[$mes]['quantidade'] : 0) - (isset($mesesSaidas[$mes]) ? $mesesSaidas[$mes]['quantidade'] : 0),
            'valor_entradas' => isset($meses[$mes]) ? $meses[$mes]['valor_total'] : 0.0,
            'valor_saidas' => isset($mesesSaidas[$mes]) ? $mesesSaidas[$mes]['valor_total'] : 0.0,
            'valor_diferenca' => (isset($meses[$mes]) ? $meses[$mes]['valor_total'] : 0.0) - (isset($mesesSaidas[$mes]) ? $mesesSaidas[$mes]['valor_total'] : 0.0)
        ];
    }
}

// Preparar dados para o gráfico
$labelsComp = [];
$dataEntradasComp = [];
$dataSaidasComp = [];
$dataValorEntradasComp = [];
$dataValorSaidasComp = [];
$maxValue = 0;
$maxValor = 0;
foreach ($mesesComp as $m) {
    $labelsComp[] = date('m/Y', strtotime($m['mes'].'-01'));
    $dataEntradasComp[] = $m['entradas'];
    $dataSaidasComp[] = $m['saidas'];
    $dataValorEntradasComp[] = round($m['valor_entradas'], 2);
    $dataValorSaidasComp[] = round($m['valor_saidas'], 2);
    $maxValue = max($maxValue, $m['entradas'], $m['saidas']);
    $maxValor = max($maxValor, $m['valor_entradas'], $m['valor_saidas']);
}
$maxValue = ceil($maxValue * 1.1);
$maxValor = ceil($maxValor * 1.1);

$mesAtual = date('Y-m');
// Filtro de mês para CNPJ
$mesFiltrosCNPJ = isset($_GET['filtro_mes_cnpj']) ? $_GET['filtro_mes_cnpj'] : $mesAtual;
// Variável para filtro de empresa
$empresaSelecionada = isset($_GET['empresa_cnpj']) ? $_GET['empresa_cnpj'] : '';

// Filtro na query se empresa selecionada
$sqlFiltroEmpresa = '';
$params = [$mesFiltrosCNPJ];
$types = 's';
if ($empresaSelecionada) {
    $sqlFiltroEmpresa = ' AND e.cnpj = ? ';
    $params[] = $empresaSelecionada;
    $types .= 's';
}
$sqlValorPorEmpresa = "
    SELECT 
        s.empresa_destino as codigo_empresa,
        e.nome_empresa,
        e.cnpj,
        SUM(ps.quantidade * p.valor) as valor_total
    FROM saidas_estoque s
    JOIN produtos_saida ps ON s.id = ps.saida_id
    JOIN produtos p ON ps.codigo = p.codigo
    LEFT JOIN empresas e ON s.empresa_destino = e.codigo_empresa
    WHERE DATE_FORMAT(s.data_saida, '%Y-%m') = ?
    AND (s.status IS NULL OR s.status = 'ativa')
    $sqlFiltroEmpresa
    GROUP BY s.empresa_destino, e.cnpj
";
$stmt = $conn->prepare($sqlValorPorEmpresa);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$resSaidas = $stmt->get_result();
$valoresPorEmpresa = [];
// 1. Saídas diretas
while ($row = $resSaidas->fetch_assoc()) {
    $chave = $row['codigo_empresa'];
    if (!$chave) continue;
    if (!isset($valoresPorEmpresa[$chave])) {
        $valoresPorEmpresa[$chave] = [
            'codigo_empresa' => $chave,
            'nome_empresa' => $row['nome_empresa'],
            'cnpj' => $row['cnpj'],
            'valor_total' => 0.0
        ];
    }
    $valoresPorEmpresa[$chave]['valor_total'] += (float)$row['valor_total'];
}
// 2. Solicitações concluídas - Requisições
$sqlReq = "SELECT r.empresa as codigo_empresa, e.nome_empresa, e.cnpj, r.codigo_solicitacao FROM requisicoes r LEFT JOIN empresas e ON r.empresa = e.codigo_empresa WHERE r.status = 'concluido' AND DATE_FORMAT(r.data_solicitacao, '%Y-%m') = ?";
$stmt = $conn->prepare($sqlReq);
$stmt->bind_param('s', $mesFiltrosCNPJ);
$stmt->execute();
$resReq = $stmt->get_result();
while ($row = $resReq->fetch_assoc()) {
    $chave = $row['codigo_empresa'];
    if (!$chave) continue;
    $sqlItens = "SELECT i.quantidade, p.valor FROM itens_solicitacao i JOIN produtos p ON i.produto = p.codigo WHERE i.codigo_solicitacao = ?";
    $stmtItens = $conn->prepare($sqlItens);
    $stmtItens->bind_param('i', $row['codigo_solicitacao']);
    $stmtItens->execute();
    $resItens = $stmtItens->get_result();
    $valor = 0.0;
    while ($item = $resItens->fetch_assoc()) {
        $valor += ((float)$item['quantidade']) * ((float)$item['valor']);
    }
    if (!isset($valoresPorEmpresa[$chave])) {
        $valoresPorEmpresa[$chave] = [
            'codigo_empresa' => $chave,
            'nome_empresa' => $row['nome_empresa'],
            'cnpj' => $row['cnpj'],
            'valor_total' => 0.0
        ];
    }
    $valoresPorEmpresa[$chave]['valor_total'] += $valor;
}
// 3. Solicitações concluídas - Pedidos Mensais
$sqlPM = "SELECT p.empresa as codigo_empresa, e.nome_empresa, e.cnpj, p.codigo_pedido, " . str_replace(' as data_solicitacao', '', $data_pedidos_mensais) . " as data_solicitacao FROM pedidos_mensais p LEFT JOIN empresas e ON p.empresa = e.codigo_empresa WHERE p.status = 'concluido' AND DATE_FORMAT(" . str_replace(' as data_solicitacao', '', $data_pedidos_mensais) . ", '%Y-%m') = ?";
$stmt = $conn->prepare($sqlPM);
$stmt->bind_param('s', $mesFiltrosCNPJ);
$stmt->execute();
$resPM = $stmt->get_result();
while ($row = $resPM->fetch_assoc()) {
    $chave = $row['codigo_empresa'];
    if (!$chave) continue;
    $sqlItens = "SELECT i.quantidade, p.valor FROM itens_pedido_mensal i JOIN produtos p ON i.produto = p.codigo WHERE i.codigo_pedido = ?";
    $stmtItens = $conn->prepare($sqlItens);
    $stmtItens->bind_param('i', $row['codigo_pedido']);
    $stmtItens->execute();
    $resItens = $stmtItens->get_result();
    $valor = 0.0;
    while ($item = $resItens->fetch_assoc()) {
        $valor += ((float)$item['quantidade']) * ((float)$item['valor']);
    }
    if (!isset($valoresPorEmpresa[$chave])) {
        $valoresPorEmpresa[$chave] = [
            'codigo_empresa' => $chave,
            'nome_empresa' => $row['nome_empresa'],
            'cnpj' => $row['cnpj'],
            'valor_total' => 0.0
        ];
    }
    $valoresPorEmpresa[$chave]['valor_total'] += $valor;
}

// Preparar arrays para o gráfico/tabela
$empresasValor = [];
$labelsValorCNPJ = [];
$dataValorCNPJ = [];
$coresValorCNPJ = [];
$cores = ['#007bff', '#6f42c1', '#007bff', '#6f42c1', '#007bff', '#6f42c1', '#007bff', '#6f42c1', '#007bff', '#6f42c1'];
foreach ($valoresPorEmpresa as $info) {
    if ($info['valor_total'] <= 0) continue;
    $empresasValor[] = [
        'codigo_empresa' => $info['codigo_empresa'],
        'nome_empresa' => $info['nome_empresa'],
        'cnpj' => $info['cnpj'],
        'valor_total' => $info['valor_total']
    ];
    $labelsValorCNPJ[] = $info['nome_empresa'];
    $dataValorCNPJ[] = round($info['valor_total'], 2);
    $coresValorCNPJ[] = $cores[count($empresasValor) % count($cores)];
}
// Garantir pelo menos um valor dummy para o gráfico se não houver dados
if (empty($labelsValorCNPJ)) {
    $labelsValorCNPJ = ['Sem dados'];
    $dataValorCNPJ = [0];
    $coresValorCNPJ = ['#cccccc'];
}

// Buscar todas as empresas para o popup
$empresasPopup = [];
$resEmp = $conn->query("SELECT cnpj, fantasia_empresa, nome_empresa FROM empresas ORDER BY fantasia_empresa, nome_empresa");
while ($row = $resEmp->fetch_assoc()) {
    $empresasPopup[] = $row;
}

// ================= GASTOS POR SETOR =================
// Filtro de mês para Gastos por Setor
$mesFiltroSetor = isset($_GET['filtro_mes_setor']) ? $_GET['filtro_mes_setor'] : $mesAtual;
$gastosPorSetor = [];
$colaboradoresPorSetor = [];

// 1. Saídas diretas (registro-saidas.php)
$sqlSaidasSetor = "SELECT s.id as saida_id, s.setor_destinatario, s.destinatario, ps.codigo, ps.quantidade, p.valor
    FROM saidas_estoque s
    JOIN produtos_saida ps ON s.id = ps.saida_id
    JOIN produtos p ON ps.codigo = p.codigo
    WHERE (s.status IS NULL OR s.status = 'ativa') AND s.setor_destinatario IS NOT NULL AND s.setor_destinatario != ''
    AND DATE_FORMAT(s.data_saida, '%Y-%m') = ?";
$stmt = $conn->prepare($sqlSaidasSetor);
$stmt->bind_param('s', $mesFiltroSetor);
$stmt->execute();
$resSaidasSetor = $stmt->get_result();
while ($row = $resSaidasSetor->fetch_assoc()) {
    $setor = $row['setor_destinatario'] ?: 'Não informado';
    $valorTotal = $row['quantidade'] * $row['valor'];
    if (!isset($gastosPorSetor[$setor])) {
        $gastosPorSetor[$setor] = ['qtd' => 0, 'valor' => 0.0, 'colaboradores' => []];
    }
    $gastosPorSetor[$setor]['qtd'] += $row['quantidade'];
    $gastosPorSetor[$setor]['valor'] += $valorTotal;
    $gastosPorSetor[$setor]['colaboradores'][$row['destinatario']] = true;
}

// 2. Requisições e pedidos mensais concluídos (requisicoes-feitas.php)
// Buscar requisições concluídas
$sqlReqs = "SELECT r.codigo_solicitacao, r.funcionario, r.funcao, r.status FROM requisicoes r WHERE r.status = 'concluido' AND DATE_FORMAT(r.data_solicitacao, '%Y-%m') = ?";
$stmt = $conn->prepare($sqlReqs);
$stmt->bind_param('s', $mesFiltroSetor);
$stmt->execute();
$resReqs = $stmt->get_result();
while ($row = $resReqs->fetch_assoc()) {
    $nome = $row['funcionario'];
    // Buscar setor na tabela pessoas
    $setor = '';
    $resPessoa = $conn->query("SELECT setor FROM pessoas WHERE nome = '".$conn->real_escape_string($nome)."' LIMIT 1");
    if ($resPessoa && $p = $resPessoa->fetch_assoc()) {
        $setor = $p['setor'];
    }
    $setor = $setor ?: 'Não informado';
    // Buscar itens da requisição
    $sqlItens = "SELECT i.quantidade, p.valor FROM itens_solicitacao i JOIN produtos p ON i.produto = p.codigo WHERE i.codigo_solicitacao = " . intval($row['codigo_solicitacao']);
    $resItens = $conn->query($sqlItens);
    $qtd = 0; $valor = 0.0;
    while ($item = $resItens->fetch_assoc()) {
        $qtd += (int)$item['quantidade'];
        $valor += ((float)$item['quantidade']) * ((float)$item['valor']);
    }
    if (!isset($gastosPorSetor[$setor])) {
        $gastosPorSetor[$setor] = ['qtd' => 0, 'valor' => 0.0, 'colaboradores' => []];
    }
    $gastosPorSetor[$setor]['qtd'] += $qtd;
    $gastosPorSetor[$setor]['valor'] += $valor;
    $gastosPorSetor[$setor]['colaboradores'][$nome] = true;
}
// Buscar pedidos mensais concluídos
$sqlPMs = "SELECT p.codigo_pedido, p.destinatario, p.funcao, p.status FROM pedidos_mensais p WHERE p.status = 'concluido' AND DATE_FORMAT(" . str_replace(' as data_solicitacao', '', $data_pedidos_mensais) . ", '%Y-%m') = ?";
$stmt = $conn->prepare($sqlPMs);
$stmt->bind_param('s', $mesFiltroSetor);
$stmt->execute();
$resPMs = $stmt->get_result();
while ($row = $resPMs->fetch_assoc()) {
    $nome = $row['destinatario'];
    // Buscar setor na tabela pessoas
    $setor = '';
    $resPessoa = $conn->query("SELECT setor FROM pessoas WHERE nome = '".$conn->real_escape_string($nome)."' LIMIT 1");
    if ($resPessoa && $p = $resPessoa->fetch_assoc()) {
        $setor = $p['setor'];
    }
    $setor = $setor ?: 'Não informado';
    // Buscar itens do pedido mensal
    $sqlItens = "SELECT i.quantidade, p.valor FROM itens_pedido_mensal i JOIN produtos p ON i.produto = p.codigo WHERE i.codigo_pedido = " . intval($row['codigo_pedido']);
    $resItens = $conn->query($sqlItens);
    $qtd = 0; $valor = 0.0;
    while ($item = $resItens->fetch_assoc()) {
        $qtd += (int)$item['quantidade'];
        $valor += ((float)$item['quantidade']) * ((float)$item['valor']);
    }
    if (!isset($gastosPorSetor[$setor])) {
        $gastosPorSetor[$setor] = ['qtd' => 0, 'valor' => 0.0, 'colaboradores' => []];
    }
    $gastosPorSetor[$setor]['qtd'] += $qtd;
    $gastosPorSetor[$setor]['valor'] += $valor;
    $gastosPorSetor[$setor]['colaboradores'][$nome] = true;
}
// Preparar dados para gráfico e tabela
$labelsSetor = [];
$dataValorSetor = [];
$coresSetor = ['#007bff', '#28a745', '#ffc107', '#fd7e14', '#6f42c1', '#e74c3c', '#20c997', '#17a2b8', '#f39c12', '#3b5bdb'];
$corIndex = 0;
foreach ($gastosPorSetor as $setor => $info) {
    $labelsSetor[] = $setor;
    $dataValorSetor[] = round($info['valor'], 2);
}

// ================= VALOR TOTAL POR CONTRATO (MENSAL/ANUAL) =================
// Filtro de mês/ano
$mesFiltro = isset($_GET['filtro_mes_contrato']) ? $_GET['filtro_mes_contrato'] : date('Y-m');
$contratosData = [];

// 1. Solicitações e pedidos mensais concluídos
$sqlSolicitacoesContratos = "
    (SELECT contrato, $data_requisicoes, 'requisicao' as tipo, codigo_solicitacao as codigo FROM requisicoes WHERE status = 'concluido' AND contrato IS NOT NULL AND contrato != '' AND DATE_FORMAT(" . ($col_data_requisicoes ?: 'NOW()') . ", '%Y-%m') = ?)
    UNION ALL
    (SELECT contrato, $data_pedidos_mensais, 'pedido_mensal' as tipo, codigo_pedido as codigo FROM pedidos_mensais WHERE status = 'concluido' AND contrato IS NOT NULL AND contrato != '' AND DATE_FORMAT(" . ($col_data_pedidos_mensais ?: 'NOW()') . ", '%Y-%m') = ?)
";
$stmt = $conn->prepare($sqlSolicitacoesContratos);
$stmt->bind_param('ss', $mesFiltro, $mesFiltro);
$stmt->execute();
$resContratos = $stmt->get_result();
while ($row = $resContratos->fetch_assoc()) {
    $contrato = $row['contrato'] ?: 'Não informado';
    $data = $row['data_solicitacao'];
    $mesAno = date('m/Y', strtotime($data));
    $qtd = 0; $valor = 0.0;
    if ($row['tipo'] === 'requisicao') {
      $sqlItens = "SELECT i.quantidade, p.valor FROM itens_solicitacao i JOIN produtos p ON i.produto = p.codigo WHERE i.codigo_solicitacao = ?";
      $stmtItens = $conn->prepare($sqlItens);
      $stmtItens->bind_param('i', $row['codigo']);
      $stmtItens->execute();
      $resItens = $stmtItens->get_result();
      while ($item = $resItens->fetch_assoc()) {
          $qtd += (int)$item['quantidade'];
          $valor += ((float)$item['quantidade']) * ((float)$item['valor']);
      }
  } else if ($row['tipo'] === 'pedido_mensal') {
      $sqlItens = "SELECT i.quantidade, p.valor FROM itens_pedido_mensal i JOIN produtos p ON i.produto = p.codigo WHERE i.codigo_pedido = ?";
      $stmtItens = $conn->prepare($sqlItens);
      $stmtItens->bind_param('i', $row['codigo']);
      $stmtItens->execute();
      $resItens = $stmtItens->get_result();
      while ($item = $resItens->fetch_assoc()) {
          $qtd += (int)$item['quantidade'];
          $valor += ((float)$item['quantidade']) * ((float)$item['valor']);
      }
  }
    if (!isset($contratosData[$contrato])) {
        $contratosData[$contrato] = ['qtd' => 0, 'valor' => 0.0, 'mesAno' => $mesAno];
    }
    $contratosData[$contrato]['qtd'] += $qtd;
    $contratosData[$contrato]['valor'] += $valor;
}

// 2. Saídas diretas (registro-saidas.php)
$sqlSaidasContrato = "
    SELECT s.id as saida_id, s.empresa_destino, s.data_saida, ps.quantidade, p.valor, e.contrato
    FROM saidas_estoque s
    JOIN produtos_saida ps ON s.id = ps.saida_id
    JOIN produtos p ON ps.codigo = p.codigo
    LEFT JOIN empresas e ON s.empresa_destino = e.codigo_empresa
    WHERE (s.status IS NULL OR s.status = 'ativa') AND e.contrato IS NOT NULL AND e.contrato != '' AND DATE_FORMAT(s.data_saida, '%Y-%m') = ?
";
$stmt = $conn->prepare($sqlSaidasContrato);
$stmt->bind_param('s', $mesFiltro);
$stmt->execute();
$resSaidasContrato = $stmt->get_result();
while ($row = $resSaidasContrato->fetch_assoc()) {
    $contrato = $row['contrato'] ?: 'Não informado';
    $mesAno = date('m/Y', strtotime($row['data_saida']));
    $valor = ((float)$row['quantidade']) * ((float)$row['valor']);
    if (!isset($contratosData[$contrato])) {
        $contratosData[$contrato] = ['qtd' => 0, 'valor' => 0.0, 'mesAno' => $mesAno];
    }
    $contratosData[$contrato]['qtd'] += (int)$row['quantidade'];
    $contratosData[$contrato]['valor'] += $valor;
}

// Preparar arrays para gráfico e tabela
$labelsContrato = [];
$dataValorContrato = [];
$dadosTabelaContrato = [];
foreach ($contratosData as $contrato => $info) {
    $labelsContrato[] = $contrato;
    $dataValorContrato[] = round($info['valor'], 2);
    $dadosTabelaContrato[] = [
        'contrato' => $contrato,
        'qtd' => $info['qtd'],
        'valor' => $info['valor'],
        'mesAno' => $info['mesAno']
    ];
}

// ================= EPIs PRÓXIMOS DO VENCIMENTO =================
$filtro_epi_empresa = isset($_GET['filtro_epi_empresa']) ? $_GET['filtro_epi_empresa'] : '';
$filtro_epi_setor = isset($_GET['filtro_epi_setor']) ? $_GET['filtro_epi_setor'] : '';
$filtro_epi_nome = isset($_GET['filtro_epi_nome']) ? $_GET['filtro_epi_nome'] : '';
$filtro_epi_dias = isset($_GET['filtro_epi_dias']) ? (int)$_GET['filtro_epi_dias'] : 30; // Padrão 30 dias

$sql_epis_vencer = "
    SELECT 
        pessoas.nome as funcionario_nome,
        pessoas.posto,
        pessoas.setor,
        pessoas.funcao,
        produtos.nome as epi_nome,
        ppe.data_entrega,
        produtos.validade_uso,
        DATEDIFF(DATE_ADD(ppe.data_entrega, INTERVAL produtos.validade_uso MONTH), CURDATE()) as dias_restantes
    FROM 
        pessoa_epi ppe
    INNER JOIN (
        SELECT 
            pessoa_id, 
            produto_id, 
            MAX(data_entrega) as max_data_entrega
        FROM 
            pessoa_epi
        GROUP BY 
            pessoa_id, produto_id
    ) as pe_recente 
        ON ppe.pessoa_id = pe_recente.pessoa_id 
        AND ppe.produto_id = pe_recente.produto_id 
        AND ppe.data_entrega = pe_recente.max_data_entrega
    JOIN 
        produtos ON ppe.produto_id = produtos.codigo
    JOIN 
        pessoas ON ppe.pessoa_id = pessoas.id
    WHERE 
        pessoas.status = 'ativo'
        AND produtos.validade_uso > 0
        AND DATEDIFF(DATE_ADD(ppe.data_entrega, INTERVAL produtos.validade_uso MONTH), CURDATE()) BETWEEN 0 AND ?
";

$params_epi = [$filtro_epi_dias];
$types_epi = 'i';

if ($filtro_epi_empresa) {
    $sql_epis_vencer .= " AND pessoas.posto = ?";
    $params_epi[] = $filtro_epi_empresa;
    $types_epi .= 's';
}
if ($filtro_epi_setor) {
    $sql_epis_vencer .= " AND pessoas.setor = ?";
    $params_epi[] = $filtro_epi_setor;
    $types_epi .= 's';
}
if ($filtro_epi_nome) {
    $sql_epis_vencer .= " AND pessoas.nome LIKE ?";
    $params_epi[] = "%" . $filtro_epi_nome . "%";
    $types_epi .= 's';
}

$sql_epis_vencer .= " ORDER BY dias_restantes ASC";

$stmt_epis = $conn->prepare($sql_epis_vencer);
if ($stmt_epis) {
    $stmt_epis->bind_param($types_epi, ...$params_epi);
    $stmt_epis->execute();
    $result_epis = $stmt_epis->get_result();
    $epis_a_vencer = [];
    while ($row = $result_epis->fetch_assoc()) {
        $epis_a_vencer[] = $row;
    }
} else {
    $epis_a_vencer = [];
}
// Obter empresas, setores para filtros
$empresas_epi = $conn->query("SELECT DISTINCT posto FROM pessoas WHERE posto IS NOT NULL AND posto != '' ORDER BY posto")->fetch_all(MYSQLI_ASSOC);
$setores_epi = $conn->query("SELECT DISTINCT setor FROM pessoas WHERE setor IS NOT NULL AND setor != '' ORDER BY setor")->fetch_all(MYSQLI_ASSOC);


?>
    <!-- Container independente para posicionamento livre dos cards -->
    <div class="cards-independent-container">
      <!-- Container específico: Total de Itens no Estoque -->
      <div class="card-container-estoque">
        <div class="modern-card card-estoque">
      <div class="card-content">
        <!-- Botões de ação no canto superior direito -->
        <div class="card-actions-top">
          <button class="table-toggle-btn" onclick="toggleTable('table-estoque', this)" title="Mostrar tabela">
            <i class="fas fa-table"></i>
          </button>
          <div class="export-dropdown">
            <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-estoque')" title="Opções de exportação">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <div class="export-dropdown-content" id="dropdown-estoque">
              <button class="export-dropdown-item excel" onclick="exportarTabela('estoque-tabela', 'estoque_atual.xlsx')">
                <i class="fas fa-file-excel"></i>
                Exportar Excel
              </button>
              <button class="export-dropdown-item pdf" onclick="window.print()">
                <i class="fas fa-file-pdf"></i>
                Exportar PDF
              </button>
            </div>
          </div>
        </div>

        <div class="card-content-with-table">
          <div class="chart-section">
            <div class="metric-indicator">
              <div class="metric-icon success">
                <i class="fas fa-box"></i>
              </div>
              <div class="metric-content">
                <h3 id="estoqueTotalIndicador"><?php echo number_format($totalGeral, 0, ',', '.'); ?></h3>
                <p>Itens em estoque</p>
              </div>
            </div>

            <div class="chart-with-legend">
              <div class="chart-container-pie">
                <canvas id="estoqueChart"></canvas>
              </div>
              <div class="chart-legend-right" id="estoqueChartLegend">
                <!-- Legenda será gerada dinamicamente -->
              </div>
            </div>
          </div>

          <div class="table-section" id="table-estoque">
            
            <div class="table-container">
              <table class="modern-table" id="estoque-tabela">
                <thead>
                  <tr>
                    <th>Categoria</th>
                    <th>Produtos</th>
                    <th>Quantidade</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($categorias as $cat): ?>
                  <tr>
                    <td><?php echo htmlspecialchars($cat['categoria']); ?></td>
                    <td><?php echo number_format($cat['total_produtos'], 0, ',', '.'); ?></td>
                    <td><?php echo number_format($cat['quantidade_total'], 0, ',', '.'); ?></td>
                  </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div> <!-- Fim container estoque -->

    <!-- Container específico: Solicitações Pendentes -->
    <div class="card-container-solicitacoes">
      <div class="modern-card card-solicitacoes">
      <div class="card-content">
        <!-- Botão de ação no canto superior direito -->
        <div class="card-actions-top">
          <a href="todas-solitacoes-estoque.php" class="btn-modern" title="Ver todas as solicitações">
            <i class="fas fa-arrow-right"></i> Ver todas
          </a>
        </div>

        <div class="metric-indicator">
          <div class="metric-icon primary">
            <i class="fas fa-tasks"></i>
          </div>
          <div class="metric-content">
            <h3 id="solicitacoesPendentesIndicador"><?php echo number_format($pendentesTotal, 0, ',', '.'); ?></h3>
            <p>Solicitações pendentes</p>
          </div>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: 16px; margin-top: 24px;">
          <div style="text-align: center; padding: 16px; background: var(--light-bg); border-radius: var(--radius-md); border-left: 4px solid var(--success-color);">
            <div style="font-size: 20px; font-weight: 600; color: var(--success-color);"><?php echo $pendentesPorTipo['requisicao']; ?></div>
            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px; display: flex; align-items: center; justify-content: center; gap: 4px;">
              <i class="fas fa-file-alt"></i> Requisições
            </div>
          </div>
          <div style="text-align: center; padding: 16px; background: var(--light-bg); border-radius: var(--radius-md); border-left: 4px solid var(--info-color);">
            <div style="font-size: 20px; font-weight: 600; color: var(--info-color);"><?php echo $pendentesPorTipo['pedido_mensal']; ?></div>
            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px; display: flex; align-items: center; justify-content: center; gap: 4px;">
              <i class="fas fa-calendar-alt"></i> Mensais
            </div>
          </div>
          <div style="text-align: center; padding: 16px; background: var(--light-bg); border-radius: var(--radius-md); border-left: 4px solid var(--warning-color);">
            <div style="font-size: 20px; font-weight: 600; color: var(--warning-color);"><?php echo $pendentesPorTipo['pedido_especial']; ?></div>
            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px; display: flex; align-items: center; justify-content: center; gap: 4px;">
              <i class="fas fa-star"></i> Especiais
            </div>
          </div>
        </div>
      </div>
    </div> <!-- Fim container solicitações -->
    </div> <!-- Fim container independente -->

    <!-- Container específico: Produtos com Estoque Mínimo -->
    <div class="card-container-estoque-minimo">
      <div class="modern-card card-estoque-minimo">
        <div class="card-content">
          <!-- Botões de ação no canto superior direito -->
          <div class="card-actions-top">
            <select id="filtroEstMin" class="btn-modern" style=" background: white; padding: 6px 10px;" onchange="filtrarEstMin()">
              <option value="5" <?php if($limiteEstMin==5) echo 'selected'; ?>>5 itens</option>
              <option value="10" <?php if($limiteEstMin==10) echo 'selected'; ?>>10 itens</option>
              <option value="20" <?php if($limiteEstMin==20) echo 'selected'; ?>>20 itens</option>
              <option value="50" <?php if($limiteEstMin==50) echo 'selected'; ?>>50 itens</option>
              <option value="todos" <?php if($limiteEstMin==='todos') echo 'selected'; ?>>Todos</option>
            </select>
            <button class="table-toggle-btn" onclick="toggleTable('table-estoque-minimo', this)" title="Mostrar tabela">
              <i class="fas fa-table"></i>
            </button>
            <div class="export-dropdown">
              <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-estoque-minimo')" title="Opções de exportação">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <div class="export-dropdown-content" id="dropdown-estoque-minimo">
                <button class="export-dropdown-item excel" onclick="exportarTabela('estoque-minimo-tabela', 'estoque_minimo.xlsx')">
                  <i class="fas fa-file-excel"></i>
                  Exportar Excel
                </button>
                <button class="export-dropdown-item pdf" onclick="imprimirTabela('estoque-minimo-tabela')">
                  <i class="fas fa-file-pdf"></i>
                  Exportar PDF
                </button>
              </div>
            </div>
          </div>

          <div class="card-content-with-table">
            <div class="chart-section">
              <div class="metric-indicator">
                <div class="metric-icon warning">
                  <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="metric-content">
                  <h3><?php echo $totalProdutosCriticos; ?></h3>
                  <p>Produtos zerados ou abaixo do mínimo</p>
                </div>
              </div>

              <div class="chart-container">
                <canvas id="estoqueMinChart"></canvas>
              </div>
            </div>

            <div class="table-section" id="table-estoque-minimo">
              <div class="table-section-title">
                <i class="fas fa-exclamation-triangle"></i>
                Produtos com Estoque Baixo
              </div>
              <div class="table-container" style="max-height: 400px; overflow-y: auto;">
                <table class="modern-table" id="estoque-minimo-tabela">
                  <thead>
                    <tr>
                      <th>Produto</th>
                      <th>Estoque Atual</th>
                      <th>Estoque Mínimo</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody id="tbodyEstMin">
                    <?php foreach ($produtosEstMinExibir as $prod): ?>
                    <tr>
                      <td><?php echo htmlspecialchars($prod['nome']); ?></td>
                      <td><?php echo number_format($prod['quantidade'], 0, ',', '.'); ?></td>
                      <td><?php echo number_format($prod['estoque_minimo'], 0, ',', '.'); ?></td>
                      <td>
                        <?php if ($prod['status'] === 'zerado'): ?>
                          <span class="status-badge danger" title="Estoque zerado">Zerado</span>
                        <?php elseif ($prod['status'] === 'abaixo'): ?>
                          <span class="status-badge warning" title="Abaixo do mínimo">Baixo</span>
                        <?php else: ?>
                          <span class="status-badge success" title="Estoque saudável">Normal</span>
                        <?php endif; ?>
                      </td>
                    </tr>
                    <?php endforeach; ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> <!-- Fim container estoque mínimo -->

    <!-- Container específico: comparativo -->
     <div class="card-container-comparativo">
      <div class="modern-card card-comparativo">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-chart-line"></i>
            Comparativo Entradas vs Saídas (últimos 6 meses)
          </h3>
          <div class="action-buttons">
            <select id="filtroComparativo" class="btn-modern" style="border: 1px solid var(--border-color); background: white; padding: 6px 10px;">
              <option value="quantidade">Quantidade</option>
              <option value="valor" selected>Valor (R$)</option>
            </select>
            <button class="table-toggle-btn" onclick="toggleTable('table-comparativo', this)" title="Mostrar tabela">
              <i class="fas fa-table"></i>
            </button>
            <div class="export-dropdown">
              <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-comparativo')" title="Opções de exportação">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <div class="export-dropdown-content" id="dropdown-comparativo">
                <button class="export-dropdown-item excel" onclick="exportarTabela('comparativo-tabela', 'comparativo_entradas_saidas.xlsx')">
                  <i class="fas fa-file-excel"></i>
                  Exportar Excel
                </button>
                <button class="export-dropdown-item pdf" onclick="imprimirTabela('comparativo-tabela')">
                  <i class="fas fa-file-pdf"></i>
                  Exportar PDF
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="card-content">
          <div class="card-content-with-table">
            <div class="chart-section">
              <div class="chart-container" style="height: 450px;">
                <canvas id="comparativoChart"></canvas>
              </div>
            </div>

            <div class="table-section" id="table-comparativo">
              <div class="table-section-title">
                <i class="fas fa-balance-scale"></i>
                Dados Comparativos Mensais
              </div>
              <div class="table-container" style="max-height: 400px; overflow-y: auto;">
                <table class="modern-table" id="comparativo-tabela">
                  <thead>
                    <tr>
                      <th>Mês</th>
                      <th>Entradas</th>
                      <th>Saídas</th>
                      <th>Diferença</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php foreach ($mesesComp as $m): ?>
                    <tr>
                      <td><?php echo date('m/Y', strtotime($m['mes'].'-01')); ?></td>
                      <td><?php echo number_format($m['entradas'], 0, ',', '.'); ?></td>
                      <td><?php echo number_format($m['saidas'], 0, ',', '.'); ?></td>
                      <td>
                        <?php if ($m['diferenca'] >= 0): ?>
                          <span class="status-badge success">+<?php echo number_format($m['diferenca'], 0, ',', '.'); ?></span>
                        <?php else: ?>
                          <span class="status-badge danger"><?php echo number_format($m['diferenca'], 0, ',', '.'); ?></span>
                        <?php endif; ?>
                      </td>
                    </tr>
                    <?php endforeach; ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Fim container comparativo -->

    <!-- Container para Cards de Entradas e Saídas -->
    <div class="cards-entradas-saidas-container">
      <!-- Card de Entradas -->
      <div class="modern-card card-entradas">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-arrow-down"></i>
            Entradas por Mês (últimos 12 meses)
          </h3>
          <div class="action-buttons">
            <select id="filtroEntradas" class="btn-modern" style="border: 1px solid var(--border-color); background: white; padding: 6px 10px;">
              <option value="quantidade">Quantidade</option>
              <option value="valor" selected>Valor (R$)</option>
            </select>
            <button class="table-toggle-btn" onclick="toggleTable('table-entradas', this)" title="Mostrar tabela">
              <i class="fas fa-table"></i>
            </button>
            <div class="export-dropdown">
              <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-entradas')" title="Opções de exportação">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <div class="export-dropdown-content" id="dropdown-entradas">
                <button class="export-dropdown-item excel" onclick="exportarTabela('entradas-mes-tabela', 'entradas_por_mes.xlsx')">
                  <i class="fas fa-file-excel"></i>
                  Exportar Excel
                </button>
                <button class="export-dropdown-item pdf" onclick="imprimirTabela('entradas-mes-tabela')">
                  <i class="fas fa-file-pdf"></i>
                  Exportar PDF
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="card-content">
          <div class="card-content-with-table">
            <div class="chart-section">
              <div class="chart-container">
                <canvas id="entradasMesChart" style="display: block; box-sizing: border-box; height: 220px; width: 660px;" width="660" height="220"></canvas>
              </div>
            </div>

            <div class="table-section" id="table-entradas">
              <div class="table-section-title">
                <i class="fas fa-list"></i>
                Detalhes das Entradas Mensais
              </div>
              <div class="table-container" style="max-height: 200px; overflow-y: auto; padding-bottom: 10px;">
                <table class="modern-table" id="entradas-mes-tabela">
                  <thead>
                    <tr>
                      <th>Mês</th>
                      <th>Total de Entradas</th>
                      <th>Quantidade</th>
                      <th>Valor Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php for($i=0; $i<count($labelsEntradas); $i++): ?>
                    <tr>
                      <td><?php echo $labelsEntradas[$i]; ?></td>
                      <td><?php echo number_format($dataEntradas[$i], 0, ',', '.'); ?></td>
                      <td><?php echo number_format($dataQuantidades[$i], 0, ',', '.'); ?></td>
                      <td>
                        <span style="font-weight: 600; color: var(--success-color);">
                          R$ <?php echo number_format($dataValores[$i], 2, ',', '.'); ?>
                        </span>
                      </td>
                    </tr>
                    <?php endfor; ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Card de Saídas -->
      <div class="modern-card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-arrow-up"></i>
            Saídas por Mês (últimos 12 meses)
          </h3>
          <div class="action-buttons">
            <select id="filtroSaidas" class="btn-modern" style="border: 1px solid var(--border-color); background: white; padding: 6px 10px;">
              <option value="quantidade">Quantidade</option>
              <option value="valor" selected>Valor (R$)</option>
            </select>
            <button class="table-toggle-btn" onclick="toggleTable('table-saidas', this)" title="Mostrar tabela">
              <i class="fas fa-table"></i>
            </button>
            <div class="export-dropdown">
              <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-saidas')" title="Opções de exportação">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <div class="export-dropdown-content" id="dropdown-saidas">
                <button class="export-dropdown-item excel" onclick="exportarTabela('saidas-mes-tabela', 'saidas_por_mes.xlsx')">
                  <i class="fas fa-file-excel"></i>
                  Exportar Excel
                </button>
                <button class="export-dropdown-item pdf" onclick="imprimirTabela('saidas-mes-tabela')">
                  <i class="fas fa-file-pdf"></i>
                  Exportar PDF
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="card-content">
          <div class="card-content-with-table">
            <div class="chart-section">
              <div class="chart-container">
                <canvas id="saidasMesChart" style="display: block; box-sizing: border-box; height: 220px; width: 660px;" width="660" height="220"></canvas>
              </div>
            </div>

            <div class="table-section" id="table-saidas">
              <div class="table-section-title">
                <i class="fas fa-list"></i>
                Detalhes das Saídas Mensais
              </div>
              <div class="table-container" style="max-height: 200px; overflow-y: auto; padding-bottom: 10px;">
                <table class="modern-table" id="saidas-mes-tabela">
                  <thead>
                    <tr>
                      <th>Mês</th>
                      <th>Total de Saídas</th>
                      <th>Quantidade</th>
                      <th>Valor Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php for($i=0; $i<count($labelsSaidas); $i++): ?>
                    <tr>
                      <td><?php echo $labelsSaidas[$i]; ?></td>
                      <td><?php echo number_format($dataSaidas[$i], 0, ',', '.'); ?></td>
                      <td><?php echo number_format($dataQuantidadesSaidas[$i], 0, ',', '.'); ?></td>
                      <td>
                        <span style="font-weight: 600; color: var(--danger-color);">
                          R$ <?php echo number_format($dataValoresSaidas[$i], 2, ',', '.'); ?>
                        </span>
                      </td>
                    </tr>
                    <?php endfor; ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> <!-- Fim container entradas e saídas -->

    <!-- Container para Cards de Validade e Top Produtos -->
    <div class="cards-validade-top-produtos-container">
      <!-- Card de Validade Próxima -->
      <div class="modern-card card-validade">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-calendar-alt"></i>
            Produtos com Validade Próxima (até 30 dias)
          </h3>
          <div class="action-buttons">
            <select id="filtroValidade" class="btn-modern" style="border: 1px solid var(--border-color); background: white; padding: 6px 10px;" onchange="filtrarValidade()">
              <option value="5" <?php if($limiteValidade==5) echo 'selected'; ?>>5 itens</option>
              <option value="10" <?php if($limiteValidade==10) echo 'selected'; ?>>10 itens</option>
              <option value="20" <?php if($limiteValidade==20) echo 'selected'; ?>>20 itens</option>
              <option value="50" <?php if($limiteValidade==50) echo 'selected'; ?>>50 itens</option>
              <option value="todos" <?php if($limiteValidade==='todos') echo 'selected'; ?>>Todos</option>
            </select>
            <button class="table-toggle-btn" onclick="toggleTable('table-validade', this)" title="Mostrar tabela">
              <i class="fas fa-table"></i>
            </button>
            <div class="export-dropdown">
              <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-validade')" title="Opções de exportação">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <div class="export-dropdown-content" id="dropdown-validade">
                <button class="export-dropdown-item excel" onclick="exportarTabela('validade-tabela', 'validade_proxima.xlsx')">
                  <i class="fas fa-file-excel"></i>
                  Exportar Excel
                </button>
                <button class="export-dropdown-item pdf" onclick="imprimirTabela('validade-tabela')">
                  <i class="fas fa-file-pdf"></i>
                  Exportar PDF
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="card-content">
          <div class="card-content-with-table">
            <div class="chart-section">
              <div class="chart-container">
                <canvas id="validadeChart"></canvas>
              </div>
            </div>

            <div class="table-section" id="table-validade">
              <div class="table-section-title">
                <i class="fas fa-calendar-times"></i>
                Produtos Próximos do Vencimento
              </div>
              <div class="table-container" style="max-height: 400px; overflow-y: auto;">
                <table class="modern-table" id="validade-tabela">
                  <thead>
                    <tr>
                      <th>Produto</th>
                      <th>Data de Validade</th>
                      <th>Quantidade</th>
                      <th>Dias Restantes</th>
                    </tr>
                  </thead>
                  <tbody id="tbodyValidade">
                    <?php foreach ($produtosValidadeExibir as $prod): ?>
                    <tr>
                      <td><?php echo htmlspecialchars($prod['nome']); ?></td>
                      <td><?php echo date('d/m/Y', strtotime($prod['validade'])); ?></td>
                      <td><?php echo number_format($prod['quantidade'], 0, ',', '.'); ?></td>
                      <td>
                        <?php if ($prod['status'] === 'vencido'): ?>
                          <span class="status-badge danger"><?php echo $prod['dias_restantes']; ?> dias</span>
                        <?php elseif ($prod['status'] === 'proximo'): ?>
                          <span class="status-badge warning"><?php echo $prod['dias_restantes']; ?> dias</span>
                        <?php else: ?>
                          <span class="status-badge success"><?php echo $prod['dias_restantes']; ?> dias</span>
                        <?php endif; ?>
                      </td>
                    </tr>
                    <?php endforeach; ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Card de Top 10 Produtos -->
      <div class="modern-card card-top-produtos">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-layer-group"></i>
          Top 10 Produtos Mais Estocados
        </h3>
        <div class="action-buttons">
          <button class="table-toggle-btn" onclick="toggleTable('table-top-estocados', this)" title="Mostrar tabela">
            <i class="fas fa-table"></i>
          </button>
          <div class="export-dropdown">
            <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-top-estocados')" title="Opções de exportação">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <div class="export-dropdown-content" id="dropdown-top-estocados">
              <button class="export-dropdown-item excel" onclick="exportarTabela('top-estocados-tabela', 'top_estocados.xlsx')">
                <i class="fas fa-file-excel"></i>
                Exportar Excel
              </button>
              <button class="export-dropdown-item pdf" onclick="imprimirTabela('top-estocados-tabela')">
                <i class="fas fa-file-pdf"></i>
                Exportar PDF
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="card-content">
        <div class="card-content-with-table">
          <div class="chart-section">
            <div class="chart-container">
              <canvas id="topEstocadosChart"></canvas>
            </div>
          </div>

          <div class="table-section" id="table-top-estocados">
            <div class="table-section-title">
              <i class="fas fa-list-ol"></i>
              Ranking Detalhado
            </div>
            <div class="table-container">
              <table class="modern-table" id="top-estocados-tabela">
                <thead>
                  <tr>
                    <th>Produto</th>
                    <th>Quantidade</th>
                    <th>Categoria</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($topEstocados as $prod): ?>
                  <tr>
                    <td><?php echo htmlspecialchars($prod['nome']); ?></td>
                    <td><?php echo number_format($prod['quantidade'], 0, ',', '.'); ?></td>
                    <td>
                      <span class="status-badge info"><?php echo htmlspecialchars($prod['categoria']); ?></span>
                    </td>
                  </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div> <!-- Fim container validade e top produtos -->









    <!-- Container para Cards de CNPJ, Gastos por Setor e Contratos -->
    <div class="cards-cnpj-gastos-contratos-container">
      <!-- Card: Valor Total de Produtos por CNPJ -->
      <div class="modern-card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-money-bill-wave"></i>
          Valor Total de Produtos por CNPJ
        </h3>
        <div class="action-buttons">
          <form method="get" style="display: flex; align-items: center; gap: 8px;">
            <label for="filtro_mes_cnpj" style="font-size: 12px; color: var(--text-secondary); white-space: nowrap;">Mês/Ano:</label>
            <input type="month" id="filtro_mes_cnpj" name="filtro_mes_cnpj" value="<?php echo htmlspecialchars($mesFiltrosCNPJ); ?>"
                   style="border: 1px solid var(--border-color); padding: 4px 8px; border-radius: 4px; font-size: 12px;"
                   onchange="this.form.submit()">
          </form>
          <button class="table-toggle-btn" onclick="toggleTable('table-cnpj', this)" title="Mostrar tabela">
            <i class="fas fa-table"></i>
          </button>
          <div class="export-dropdown">
            <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-cnpj')" title="Opções de exportação">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <div class="export-dropdown-content" id="dropdown-cnpj">
              <button class="export-dropdown-item excel" onclick="exportarTabela('valor-cnpj-tabela', 'valor_produtos_cnpj.xlsx')">
                <i class="fas fa-file-excel"></i>
                Exportar Excel
              </button>
              <button class="export-dropdown-item pdf" onclick="imprimirTabela('valor-cnpj-tabela')">
                <i class="fas fa-file-pdf"></i>
                Exportar PDF
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="card-content">
        <div class="card-content-with-table">
          <div class="chart-section">
            <?php if($empresaSelecionada): ?>
            <div style="margin-bottom: 20px; padding: 12px; background: var(--light-bg); border-radius: var(--radius-md); border-left: 4px solid var(--primary-color);">
              <strong>Empresa selecionada:</strong>
              <?php
                foreach($empresasPopup as $emp) {
                  if($emp['cnpj'] === $empresaSelecionada) {
                    echo htmlspecialchars($emp['fantasia_empresa'] ?: $emp['nome_empresa']);
                    break;
                  }
                }
              ?>
              <a href="?" style="color: var(--danger-color); margin-left: 10px; text-decoration: none; font-weight: 500;">[Limpar]</a>
            </div>
            <?php endif; ?>

            <div class="chart-container">
              <canvas id="valorCNPJChart"></canvas>
              <?php if (count($empresasValor) === 0): ?>
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: var(--text-secondary);">
                  <i class="fas fa-chart-pie" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                  <p>Sem dados para exibir</p>
                </div>
              <?php endif; ?>
            </div>
          </div>

          <div class="table-section" id="table-cnpj">
            <div class="table-section-title">
              <i class="fas fa-building"></i>
              Empresas por Valor Total
            </div>
            <div class="table-container" style="max-height: 400px; overflow-y: auto;">
              <table class="modern-table" id="valor-cnpj-tabela">
                <thead>
                  <tr>
                    <th>Empresa</th>
                    <th>CNPJ</th>
                    <th>Valor Total</th>
                    <th>Ações</th>
                  </tr>
                </thead>
                <tbody>
                  <?php if (count($empresasValor) === 0): ?>
                    <tr>
                      <td colspan="4" style="text-align: center; color: var(--text-secondary); padding: 40px;">
                        Sem dados para exibir
                      </td>
                    </tr>
                  <?php else: ?>
                    <?php foreach ($empresasValor as $empresa): ?>
                    <tr>
                      <td><?php echo htmlspecialchars($empresa['nome_empresa']); ?></td>
                      <td><code style="background: var(--light-bg); padding: 2px 6px; border-radius: 4px; font-size: 12px;"><?php echo htmlspecialchars($empresa['cnpj']); ?></code></td>
                      <td>
                        <span style="font-weight: 600; color: var(--success-color);">
                          R$ <?php echo number_format($empresa['valor_total'], 2, ',', '.'); ?>
                        </span>
                      </td>
                      <td>
                        <button class="btn-modern info" onclick="verDetalhesCNPJ('<?php echo htmlspecialchars($empresa['cnpj']); ?>', '<?php echo htmlspecialchars($empresa['nome_empresa']); ?>', '<?php echo $mesAtual; ?>')">
                          <i class="fas fa-eye"></i> Ver
                        </button>
                      </td>
                    </tr>
                    <?php endforeach; ?>
                  <?php endif; ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      </div> <!-- Fim Card CNPJ -->

      <!-- Card: Gastos por Setor -->
      <div class="modern-card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-chart-pie"></i>
            Gastos por Setor
          </h3>
          <div class="action-buttons">
            <form method="get" style="display: flex; align-items: center; gap: 8px;">
              <label for="filtro_mes_setor" style="font-size: 12px; color: var(--text-secondary); white-space: nowrap;">Mês/Ano:</label>
              <input type="month" id="filtro_mes_setor" name="filtro_mes_setor" value="<?php echo htmlspecialchars($mesFiltroSetor); ?>"
                     style="border: 1px solid var(--border-color); padding: 4px 8px; border-radius: 4px; font-size: 12px;"
                     onchange="this.form.submit()">
            </form>
            <button class="table-toggle-btn" onclick="toggleTable('table-gastos-setor', this)" title="Mostrar tabela">
              <i class="fas fa-table"></i>
            </button>
            <div class="export-dropdown">
              <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-gastos-setor')" title="Opções de exportação">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <div class="export-dropdown-content" id="dropdown-gastos-setor">
                <button class="export-dropdown-item excel" onclick="exportarTabela('gastos-setor-tabela', 'gastos_por_setor.xlsx')">
                  <i class="fas fa-file-excel"></i>
                  Exportar Excel
                </button>
                <button class="export-dropdown-item pdf" onclick="imprimirTabela('gastos-setor-tabela')">
                  <i class="fas fa-file-pdf"></i>
                  Exportar PDF
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="card-content">
          <div class="card-content-with-table">
            <div class="chart-section">
              <div class="chart-container">
                <canvas id="gastosSetorChart"></canvas>
              </div>
            </div>

            <div class="table-section" id="table-gastos-setor">
              <div class="table-section-title">
                <i class="fas fa-list"></i>
                Detalhes dos Gastos por Setor
              </div>
              <div class="table-container" style="max-height: 400px; overflow-y: auto;">
                <table class="modern-table" id="gastos-setor-tabela">
                  <thead>
                    <tr>
                      <th>Setor</th>
                      <th>Valor Total</th>
                      <th>Colaboradores</th>
                      <th>Ações</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php foreach ($gastosPorSetor as $setor => $info): ?>
                    <tr>
                      <td><?php echo htmlspecialchars($setor); ?></td>
                      <td>
                        <span style="font-weight: 600; color: var(--primary-color);">
                          R$ <?php echo number_format($info['valor'], 2, ',', '.'); ?>
                        </span>
                      </td>
                      <td>
                        <?php if (count($info['colaboradores']) > 3): ?>
                          <span class="status-badge info"><?php echo count($info['colaboradores']); ?> colaboradores</span>
                        <?php else: ?>
                          <?php echo implode(', ', array_keys($info['colaboradores'])); ?>
                        <?php endif; ?>
                      </td>
                      <td>
                        <button class="btn-action view" onclick="verDetalhesSetor('<?php echo htmlspecialchars($setor); ?>')" title="Ver detalhes">
                          <i class="fas fa-eye"></i> Ver
                        </button>
                      </td>
                    </tr>
                    <?php endforeach; ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div> <!-- Fim Card Gastos por Setor -->

      <!-- Card: Valor Total por Contrato -->
      <div class="modern-card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-file-contract"></i>
            Valor Total por Contrato (mensal/anual)
          </h3>
          <div class="action-buttons">
            <form method="get" style="display: flex; align-items: center; gap: 8px;">
              <label for="filtro_mes_contrato" style="font-size: 12px; color: var(--text-secondary); white-space: nowrap;">Mês/Ano:</label>
              <input type="month" id="filtro_mes_contrato" name="filtro_mes_contrato" value="<?php echo htmlspecialchars($mesFiltro); ?>"
                     style="border: 1px solid var(--border-color); padding: 4px 8px; border-radius: 4px; font-size: 12px;"
                     onchange="this.form.submit()">
            </form>
            <button class="table-toggle-btn" onclick="toggleTable('table-contratos', this)" title="Mostrar tabela">
              <i class="fas fa-table"></i>
            </button>
            <div class="export-dropdown">
              <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-contratos')" title="Opções de exportação">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <div class="export-dropdown-content" id="dropdown-contratos">
                <button class="export-dropdown-item excel" onclick="exportarTabela('contratos-tabela', 'valor_total_contratos.xlsx')">
                  <i class="fas fa-file-excel"></i>
                  Exportar Excel
                </button>
                <button class="export-dropdown-item pdf" onclick="imprimirTabela('contratos-tabela')">
                  <i class="fas fa-file-pdf"></i>
                  Exportar PDF
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="card-content">
          <div class="card-content-with-table">
            <div class="chart-section">
              <div class="chart-container">
                <canvas id="valorContratoChart"></canvas>
              </div>
            </div>

            <div class="table-section" id="table-contratos">
              <div class="table-section-title">
                <i class="fas fa-list"></i>
                Detalhes dos Contratos
              </div>
              <div class="table-container" style="max-height: 400px; overflow-y: auto;">
                <table class="modern-table" id="contratos-tabela">
                  <thead>
                    <tr>
                      <th>Contrato</th>
                      <th>Quantidade</th>
                      <th>Valor Total</th>
                      <th>Mês/Ano</th>
                      <th>Ações</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php foreach ($dadosTabelaContrato as $linha): ?>
                    <tr>
                      <td><?php echo htmlspecialchars($linha['contrato']); ?></td>
                      <td><?php echo number_format($linha['qtd'], 0, ',', '.'); ?></td>
                      <td>
                        <span style="font-weight: 600; color: var(--primary-color);">
                          R$ <?php echo number_format($linha['valor'], 2, ',', '.'); ?>
                        </span>
                      </td>
                      <td><?php echo $linha['mesAno']; ?></td>
                      <td>
                        <button class="btn-modern info" onclick="verDetalhesContrato('<?php echo htmlspecialchars($linha['contrato']); ?>', '<?php echo htmlspecialchars($mesFiltro); ?>')">
                          <i class="fas fa-eye"></i> Ver
                        </button>
                      </td>
                    </tr>
                    <?php endforeach; ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div> <!-- Fim Card Valor Total por Contrato -->
    </div> <!-- Fim container CNPJ, Gastos e Contratos -->

<!-- Popup de seleção de empresa -->
<div id="popupEmpresasCNPJ" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.4); z-index:9999; align-items:center; justify-content:center;">
  <div style="background:#fff; border-radius:8px; padding:24px; min-width:320px; max-width:90vw; max-height:80vh; overflow:auto; box-shadow:0 4px 16px rgba(0,0,0,0.15);">
    <h4>Selecionar Empresa</h4>
    <input type="text" id="filtroEmpresaCNPJ" class="form-control mb-2" placeholder="Pesquisar empresa..." onkeyup="filtrarEmpresasCNPJ()">
    <table class="table table-sm">
      <thead><tr><th>Nome Fantasia</th><th>CNPJ</th><th></th></tr></thead>
      <tbody id="listaEmpresasCNPJ">
        <?php foreach($empresasPopup as $emp): ?>
        <tr>
          <td><?= htmlspecialchars($emp['fantasia_empresa'] ?: $emp['nome_empresa']) ?></td>
          <td><?= htmlspecialchars($emp['cnpj']) ?></td>
          <td><button class="btn btn-success btn-sm" onclick="selecionarEmpresaCNPJ('<?= htmlspecialchars($emp['cnpj']) ?>')">Selecionar</button></td>
        </tr>
        <?php endforeach; ?>
      </tbody>
    </table>
    <button class="btn btn-secondary mt-2" onclick="fecharPopupEmpresas()">Fechar</button>
  </div>
</div>

<!-- Chart.js e exportação -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="update-chart-colors.js"></script>

<script>
// ===== SISTEMA DE DROPDOWN PARA EXPORTAÇÃO =====
// Definir função IMEDIATAMENTE para evitar erros de referência

// Função principal do dropdown
window.toggleExportDropdown = function(event, dropdownId) {
  event.stopPropagation();
  console.log('🎯 Toggle dropdown:', dropdownId);

  // Fechar todos os outros dropdowns
  document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
    if (dropdown.id !== dropdownId) {
      dropdown.classList.remove('show');
    }
  });

  // Toggle do dropdown atual
  const dropdown = document.getElementById(dropdownId);
  if (dropdown) {
    dropdown.classList.toggle('show');
    console.log(`📊 Dropdown ${dropdownId} ${dropdown.classList.contains('show') ? 'aberto' : 'fechado'}`);
  } else {
    console.error('❌ Dropdown não encontrado:', dropdownId);
  }
};

// Alias para compatibilidade
var toggleExportDropdown = window.toggleExportDropdown;

// Event listeners globais
document.addEventListener('click', function(event) {
  if (!event.target.closest('.export-dropdown')) {
    document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
      dropdown.classList.remove('show');
    });
  }
});

document.addEventListener('keydown', function(event) {
  if (event.key === 'Escape') {
    document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
      dropdown.classList.remove('show');
    });
  }
});

// Log de inicialização
console.log('✅ Export dropdown system loaded successfully!');
console.log('🔧 Function available:', typeof window.toggleExportDropdown);

// Verificação quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
  console.log('🔧 DOM loaded, dropdown system ready');

  const dropdowns = document.querySelectorAll('.export-dropdown-content');
  console.log(`📊 Found ${dropdowns.length} export dropdowns`);

  dropdowns.forEach((dropdown, index) => {
    console.log(`✅ Dropdown ${index + 1}: ${dropdown.id}`);
  });

  // Verificação final
  if (typeof window.toggleExportDropdown === 'function') {
    console.log('✅ toggleExportDropdown function is ready!');
  } else {
    console.error('❌ toggleExportDropdown function is NOT available!');
  }
});

// ===== SISTEMA DE TOGGLE DE TABELAS =====
window.toggleTable = function(tableId, buttonElement) {
  console.log('🔄 Toggle table:', tableId);

  const tableSection = document.getElementById(tableId);
  const button = buttonElement;

  if (tableSection && button) {
    const isVisible = tableSection.classList.contains('show');

    if (isVisible) {
      // Esconder tabela
      tableSection.classList.remove('show');
      button.classList.remove('active');
      button.title = 'Mostrar tabela';
      console.log('📊 Tabela escondida:', tableId);

      // Mostrar legenda do gráfico de estoque se for o card de estoque
      if (tableId === 'table-estoque') {
        const legend = document.getElementById('estoqueChartLegend');
        if (legend) {
          legend.classList.remove('hidden');
          console.log('📊 Legenda do estoque mostrada');
        }
      }
    } else {
      // Mostrar tabela
      tableSection.classList.add('show');
      button.classList.add('active');
      button.title = 'Esconder tabela';
      console.log('📊 Tabela mostrada:', tableId);

      // Para o card de estoque, não esconder a legenda pois a tabela é popup
      if (tableId === 'table-estoque') {
        const legend = document.getElementById('estoqueChartLegend');
        if (legend) {
          legend.classList.remove('hidden');
          console.log('📊 Legenda do estoque mantida visível (tabela popup)');
        }
      }
    }
  } else {
    console.error('❌ Elemento não encontrado:', { tableId, button: !!button });
  }
};

// Alias para compatibilidade
var toggleTable = window.toggleTable;



// ===== GRÁFICO DE ESTOQUE MÍNIMO =====
window.addEventListener('DOMContentLoaded', function() {
  const ctxEstoqueMin = document.getElementById('estoqueMinChart');
  if (ctxEstoqueMin) {
    // Dados dos produtos com estoque mínimo (primeiros 10 para visualização)
    const produtosEstMin = <?php echo json_encode(array_slice($produtosEstMinExibir, 0, 10)); ?>;

    if (produtosEstMin.length > 0) {
      const labels = produtosEstMin.map(p => p.nome.length > 20 ? p.nome.substring(0, 20) + '...' : p.nome);
      const estoqueAtual = produtosEstMin.map(p => parseInt(p.quantidade));
      const estoqueMinimo = produtosEstMin.map(p => parseInt(p.estoque_minimo));

      // Cores baseadas no status
      const coresBarras = produtosEstMin.map(p => {
        if (p.status === 'zerado') return window.modernChartConfig.colors.danger;
        if (p.status === 'abaixo') return window.modernChartConfig.colors.warning;
        return window.modernChartConfig.colors.success;
      });

      new Chart(ctxEstoqueMin, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [
            {
              label: 'Estoque Atual',
              data: estoqueAtual,
              backgroundColor: coresBarras,
              borderColor: coresBarras,
              borderWidth: 0,
              borderRadius: 4,
              borderSkipped: false
            },
            {
              label: 'Estoque Mínimo',
              data: estoqueMinimo,
              type: 'line',
              borderColor: window.modernChartConfig.colors.danger,
              backgroundColor: 'transparent',
              borderWidth: 2,
              pointBackgroundColor: window.modernChartConfig.colors.danger,
              pointBorderColor: '#ffffff',
              pointBorderWidth: 2,
              pointRadius: 4,
              tension: 0
            }
          ]
        },
        options: {
          ...window.modernChartConfig.options.bar,
          indexAxis: 'y',
          plugins: {
            ...window.modernChartConfig.options.bar.plugins,
            legend: {
              ...window.modernChartConfig.options.bar.plugins.legend,
              display: true,
              position: 'top'
            },
            tooltip: {
              ...window.modernChartConfig.options.bar.plugins.tooltip,
              callbacks: {
                afterLabel: function(context) {
                  const produto = produtosEstMin[context.dataIndex];
                  if (context.datasetIndex === 0) {
                    return `Status: ${produto.status === 'zerado' ? 'Zerado' : produto.status === 'abaixo' ? 'Abaixo do mínimo' : 'Normal'}`;
                  }
                  return '';
                }
              }
            }
          },
          scales: {
            x: {
              ...window.modernChartConfig.options.bar.scales.x,
              beginAtZero: true,
              title: {
                display: true,
                text: 'Quantidade',
                font: {
                  family: 'Inter, sans-serif',
                  size: 12,
                  weight: '500'
                }
              }
            },
            y: {
              ...window.modernChartConfig.options.bar.scales.y,
              title: {
                display: true,
                text: 'Produtos',
                font: {
                  family: 'Inter, sans-serif',
                  size: 12,
                  weight: '500'
                }
              }
            }
          }
        }
      });
    } else {
      // Mostrar mensagem quando não há dados
      ctxEstoqueMin.parentElement.innerHTML = `
        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
          <i class="fas fa-check-circle" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3; color: var(--success-color);"></i>
          <p><strong>Parabéns!</strong><br>Nenhum produto com estoque baixo</p>
        </div>
      `;
    }
  }
});
</script>
<script>
// Gráfico de pizza para proporção por categoria
const ctx = document.getElementById('estoqueChart').getContext('2d');
const estoqueData = {
  labels: <?php echo json_encode(array_column($categorias, 'categoria')); ?>,
  values: <?php echo json_encode(array_column($categorias, 'quantidade_total')); ?>,
  colors: window.modernChartConfig.palette
};

const estoqueChart = new Chart(ctx, {
  type: 'doughnut',
  data: {
    labels: estoqueData.labels,
    datasets: [{
      data: estoqueData.values,
      backgroundColor: estoqueData.colors,
      borderColor: '#ffffff',
      borderWidth: 2,
      hoverBorderWidth: 3
    }]
  },
  options: {
    ...window.modernChartConfig.options.pie,
    cutout: '65%',
    plugins: {
      ...window.modernChartConfig.options.pie.plugins,
      legend: {
        display: false // Desabilitar legenda padrão
      }
    },
    onHover: (event, activeElements) => {
      // Destacar item da legenda ao passar mouse no gráfico
      const legendItems = document.querySelectorAll('#estoqueChartLegend .legend-item');
      legendItems.forEach((item, index) => {
        if (activeElements.length > 0 && activeElements[0].index === index) {
          item.style.background = 'var(--light-bg)';
          item.style.transform = 'translateX(4px)';
        } else {
          item.style.background = 'transparent';
          item.style.transform = 'translateX(0)';
        }
      });
    }
  }
});

// Gerar legenda customizada
function generateCustomLegend() {
  const legendContainer = document.getElementById('estoqueChartLegend');
  const total = estoqueData.values.reduce((sum, value) => sum + parseInt(value || 0), 0);

  let legendHTML = '';
  estoqueData.labels.forEach((label, index) => {
    const value = parseInt(estoqueData.values[index]) || 0;
    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
    const color = estoqueData.colors[index];
    const displayLabel = label || 'Sem categoria';

    legendHTML += `
      <div class="legend-item" onclick="toggleChartSegment(${index})" data-index="${index}">
        <div class="legend-color" style="background-color: ${color};"></div>
        <div class="legend-text">
          <div class="legend-label">${displayLabel}</div>
          <div class="legend-value">${value.toLocaleString('pt-BR')} itens</div>
          <div class="legend-percentage">${percentage}%</div>
        </div>
      </div>
    `;
  });

  legendContainer.innerHTML = legendHTML;
}

// Função para toggle de segmentos do gráfico
function toggleChartSegment(index) {
  const meta = estoqueChart.getDatasetMeta(0);
  const segment = meta.data[index];

  if (segment.hidden === null || segment.hidden === false) {
    segment.hidden = true;
  } else {
    segment.hidden = false;
  }

  estoqueChart.update();

  // Atualizar visual da legenda
  const legendItem = document.querySelector(`#estoqueChartLegend .legend-item[data-index="${index}"]`);
  if (segment.hidden) {
    legendItem.style.opacity = '0.5';
    legendItem.style.textDecoration = 'line-through';
  } else {
    legendItem.style.opacity = '1';
    legendItem.style.textDecoration = 'none';
  }
}

// Gerar legenda após o gráfico ser criado
generateCustomLegend();

// Exportação para XLSX
function exportarTabela(tableId, filename) {
  var wb = XLSX.utils.table_to_book(document.getElementById(tableId), {sheet: "Sheet1"});
  XLSX.writeFile(wb, filename);
}

// Gráfico de barras horizontal - Top 10 Produtos Mais Estocados
const ctxTopEstocados = document.getElementById('topEstocadosChart').getContext('2d');
const topEstocadosChart = new Chart(ctxTopEstocados, {
  type: 'bar',
  data: {
    labels: <?php echo json_encode(array_column($topEstocados, 'nome')); ?>,
    datasets: [{
      label: 'Quantidade',
      data: <?php echo json_encode(array_column($topEstocados, 'quantidade')); ?>,
      backgroundColor: window.modernChartConfig.colors.primary,
      borderColor: window.modernChartConfig.colors.primary,
      borderWidth: 0,
      borderRadius: 6,
      borderSkipped: false
    }]
  },
  options: {
    ...window.modernChartConfig.options.bar,
    indexAxis: 'y',
    scales: {
      ...window.modernChartConfig.options.bar.scales,
      x: {
        ...window.modernChartConfig.options.bar.scales.x,
        beginAtZero: true,
        max: Math.max(...<?php echo json_encode(array_column($topEstocados, 'quantidade')); ?>) * 1.1
      }
    },
    plugins: {
      ...window.modernChartConfig.options.bar.plugins,
      legend: { display: false }
    }
  }
});

function imprimirTabela(tableId) {
  var printContents = document.getElementById(tableId).outerHTML;
  var originalContents = document.body.innerHTML;
  document.body.innerHTML = '<html><head><title>Imprimir</title></head><body>' + printContents + '</body></html>';
  window.print();
  document.body.innerHTML = originalContents;
  location.reload();
}

// Gráfico de barras horizontal - Produtos com Validade Próxima
const validadeLabels = <?php echo json_encode(array_column($produtosValidade, 'nome')); ?>;
const validadeData = <?php echo json_encode(array_column($produtosValidade, 'dias_restantes')); ?>;
const validadeColors = <?php echo json_encode(array_map(function($p) {
  if ($p['status'] === 'vencido') return '#ef4444';
  if ($p['status'] === 'proximo') return '#f59e0b';
  return '#10b981';
}, $produtosValidade)); ?>;
const ctxValidade = document.getElementById('validadeChart').getContext('2d');
const validadeChart = new Chart(ctxValidade, {
  type: 'bar',
  data: {
    labels: validadeLabels,
    datasets: [{
      label: 'Dias Restantes',
      data: validadeData,
      backgroundColor: validadeColors,
      borderColor: validadeColors,
      borderWidth: 0,
      borderRadius: 6,
      borderSkipped: false
    }]
  },
  options: {
    ...window.modernChartConfig.options.bar,
    indexAxis: 'y',
    scales: {
      ...window.modernChartConfig.options.bar.scales,
      x: {
        ...window.modernChartConfig.options.bar.scales.x,
        beginAtZero: true
      }
    },
    plugins: {
      ...window.modernChartConfig.options.bar.plugins,
      legend: { display: false }
    }
  }
});

function filtrarEstMin() {
  const val = document.getElementById('filtroEstMin').value;
  const url = new URL(window.location.href);
  url.searchParams.set('limiteEstMin', val);
  window.location.href = url.toString();
}
function filtrarValidade() {
  const val = document.getElementById('filtroValidade').value;
  const url = new URL(window.location.href);
  url.searchParams.set('limiteValidade', val);
  window.location.href = url.toString();
}

// Gráfico de linha para Entradas por Mês
const ctxEntradas = document.getElementById('entradasMesChart').getContext('2d');
const labelsEntradas = <?php echo json_encode($labelsEntradas); ?>;
const dataEntradasQuantidade = <?php echo json_encode($dataQuantidades); ?>;
const dataEntradasValor = <?php echo json_encode($dataValores); ?>;
let modoEntradas = 'valor';

const chartEntradas = new Chart(ctxEntradas, {
  type: 'line',
  data: {
    labels: labelsEntradas,
    datasets: [{
      label: 'Entradas',
      data: dataEntradasValor,
      borderColor: window.modernChartConfig.colors.success,
      backgroundColor: window.modernChartConfig.colors.success + '20',
      pointBackgroundColor: '#ffffff',
      pointBorderColor: window.modernChartConfig.colors.success,
      fill: true,
      tension: 0.4,
      pointRadius: 4,
      pointHoverRadius: 6,
      borderWidth: 3,
      pointBorderWidth: 2
    }]
  },
  options: {
    ...window.modernChartConfig.options.line,
    plugins: {
      ...window.modernChartConfig.options.line.plugins,
      legend: { display: false },
      tooltip: {
        titleFont: {
          family: 'Inter, sans-serif',
          size: 12,
          weight: '400'
        },
        bodyFont: {
          family: 'Inter, sans-serif',
          size: 11,
          weight: '400'
        },
        callbacks: {
          label: function(context) {
            const value = context.parsed.y;
            const formattedValue = modoEntradas === 'quantidade'
              ? value.toLocaleString('pt-BR')
              : 'R$ ' + value.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            return 'Entradas: ' + formattedValue;
          }
        }
      }
    },
    scales: {
      x: {
        ticks: {
          font: {
            family: 'Inter, sans-serif',
            size: 11,
            weight: '400'
          },
          color: '#9ca3af'
        }
      },
      y: {
        title: {
          display: true,
          text: 'Valor (R$)',
          font: {
            family: 'Inter, sans-serif',
            size: 12,
            weight: '400'
          },
          color: '#6b7280'
        },
        ticks: {
          font: {
            family: 'Inter, sans-serif',
            size: 11,
            weight: '400'
          },
          color: '#9ca3af'
        }
      }
    }
  }
});

// Event listener para filtro de entradas
document.getElementById('filtroEntradas').addEventListener('change', function() {
  modoEntradas = this.value;
  if (modoEntradas === 'quantidade') {
    chartEntradas.data.datasets[0].data = dataEntradasQuantidade;
    chartEntradas.options.scales.y.title.text = 'Quantidade';
  } else {
    chartEntradas.data.datasets[0].data = dataEntradasValor;
    chartEntradas.options.scales.y.title.text = 'Valor (R$)';
  }
  chartEntradas.update();
  atualizarTabelaEntradas();
});

function atualizarTabelaEntradas() {
  const modo = modoEntradas;
  document.querySelectorAll('#entradas-mes-tabela tbody tr').forEach((tr, idx) => {
    const quantidade = dataEntradasQuantidade[idx];
    const valor = dataEntradasValor[idx];

    if (modo === 'quantidade') {
      tr.children[2].textContent = quantidade.toLocaleString('pt-BR');
      tr.children[3].innerHTML = '<span style="font-weight: 600; color: var(--success-color);">R$ ' + valor.toLocaleString('pt-BR', {minimumFractionDigits: 2}) + '</span>';
    } else {
      tr.children[2].textContent = quantidade.toLocaleString('pt-BR');
      tr.children[3].innerHTML = '<span style="font-weight: 600; color: var(--success-color);">R$ ' + valor.toLocaleString('pt-BR', {minimumFractionDigits: 2}) + '</span>';
    }
  });
}

// Gráfico de linha para Saídas por Mês
const ctxSaidas = document.getElementById('saidasMesChart').getContext('2d');
const labelsSaidas = <?php echo json_encode($labelsSaidas); ?>;
const dataSaidasQuantidade = <?php echo json_encode($dataQuantidadesSaidas); ?>;
const dataSaidasValor = <?php echo json_encode($dataValoresSaidas); ?>;
let modoSaidas = 'valor';

const chartSaidas = new Chart(ctxSaidas, {
  type: 'line',
  data: {
    labels: labelsSaidas,
    datasets: [{
      label: 'Saídas',
      data: dataSaidasValor,
      borderColor: window.modernChartConfig.colors.danger,
      backgroundColor: window.modernChartConfig.colors.danger + '20',
      pointBackgroundColor: '#ffffff',
      pointBorderColor: window.modernChartConfig.colors.danger,
      fill: true,
      tension: 0.4,
      pointRadius: 4,
      pointHoverRadius: 6,
      borderWidth: 3,
      pointBorderWidth: 2
    }]
  },
  options: {
    ...window.modernChartConfig.options.line,
    plugins: {
      ...window.modernChartConfig.options.line.plugins,
      legend: { display: false },
      tooltip: {
        titleFont: {
          family: 'Inter, sans-serif',
          size: 13,
          weight: '600'
        },
        bodyFont: {
          family: 'Inter, sans-serif',
          size: 12
        },
        callbacks: {
          label: function(context) {
            const value = context.parsed.y;
            const formattedValue = modoSaidas === 'quantidade'
              ? value.toLocaleString('pt-BR')
              : 'R$ ' + value.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            return 'Saídas: ' + formattedValue;
          }
        }
      }
    },
    scales: {
      x: {
        ticks: {
          font: {
            family: 'Inter, sans-serif',
            size: 11,
            weight: '400'
          },
          color: '#9ca3af'
        }
      },
      y: {
        title: {
          display: true,
          text: 'Valor (R$)',
          font: {
            family: 'Inter, sans-serif',
            size: 12,
            weight: '400'
          },
          color: '#6b7280'
        },
        ticks: {
          font: {
            family: 'Inter, sans-serif',
            size: 11,
            weight: '400'
          },
          color: '#9ca3af'
        }
      }
    }
  }
});

// Event listener para filtro de saídas
document.getElementById('filtroSaidas').addEventListener('change', function() {
  modoSaidas = this.value;
  if (modoSaidas === 'quantidade') {
    chartSaidas.data.datasets[0].data = dataSaidasQuantidade;
    chartSaidas.options.scales.y.title.text = 'Quantidade';
  } else {
    chartSaidas.data.datasets[0].data = dataSaidasValor;
    chartSaidas.options.scales.y.title.text = 'Valor (R$)';
  }
  chartSaidas.update();
  atualizarTabelaSaidas();
});

function atualizarTabelaSaidas() {
  const modo = modoSaidas;
  document.querySelectorAll('#saidas-mes-tabela tbody tr').forEach((tr, idx) => {
    const quantidade = dataSaidasQuantidade[idx];
    const valor = dataSaidasValor[idx];

    if (modo === 'quantidade') {
      tr.children[2].textContent = quantidade.toLocaleString('pt-BR');
      tr.children[3].innerHTML = '<span style="font-weight: 600; color: var(--danger-color);">R$ ' + valor.toLocaleString('pt-BR', {minimumFractionDigits: 2}) + '</span>';
    } else {
      tr.children[2].textContent = quantidade.toLocaleString('pt-BR');
      tr.children[3].innerHTML = '<span style="font-weight: 600; color: var(--danger-color);">R$ ' + valor.toLocaleString('pt-BR', {minimumFractionDigits: 2}) + '</span>';
    }
  });
}

// Gráfico de linha para Comparativo Entradas vs Saídas
document.addEventListener('DOMContentLoaded', function() {
const ctxComp = document.getElementById('comparativoChart');
if (!ctxComp) {
  console.error('Canvas comparativoChart não encontrado');
  return;
}
const ctx = ctxComp.getContext('2d');
const labelsComp = <?php echo json_encode($labelsComp); ?>;
const dataEntradasComp = <?php echo json_encode($dataEntradasComp); ?>;
const dataSaidasComp = <?php echo json_encode($dataSaidasComp); ?>;
const dataValorEntradasComp = <?php echo json_encode($dataValorEntradasComp); ?>;
const dataValorSaidasComp = <?php echo json_encode($dataValorSaidasComp); ?>;
const maxValue = <?php echo $maxValue; ?>;
const maxValor = <?php echo $maxValor; ?>;
let modoComparativo = 'valor';
let chartComp = new Chart(ctx, {
    type: 'line',
    data: {
        labels: labelsComp,
        datasets: [
            {
                label: 'Entradas',
                data: dataValorEntradasComp,
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderColor: 'rgba(16, 185, 129, 1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(16, 185, 129, 1)',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
                pointHoverBackgroundColor: 'rgba(16, 185, 129, 1)',
                pointHoverBorderColor: '#ffffff',
                pointHoverBorderWidth: 3
            },
            {
                label: 'Saídas',
                data: dataValorSaidasComp,
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderColor: 'rgba(239, 68, 68, 1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(239, 68, 68, 1)',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
                pointHoverBackgroundColor: 'rgba(239, 68, 68, 1)',
                pointHoverBorderColor: '#ffffff',
                pointHoverBorderWidth: 3
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            x: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Período',
                    font: {
                        family: 'Inter, sans-serif',
                        size: 12,
                        weight: '600'
                    },
                    color: '#64748b'
                },
                grid: {
                    color: 'rgba(148, 163, 184, 0.1)',
                    borderColor: 'rgba(148, 163, 184, 0.2)'
                },
                ticks: {
                    font: {
                        family: 'Inter, sans-serif',
                        size: 11
                    },
                    color: '#64748b'
                }
            },
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Valor (R$)',
                    font: {
                        family: 'Inter, sans-serif',
                        size: 12,
                        weight: '600'
                    },
                    color: '#64748b'
                },
                grid: {
                    color: 'rgba(148, 163, 184, 0.1)',
                    borderColor: 'rgba(148, 163, 184, 0.2)'
                },
                ticks: {
                    precision: 0,
                    font: {
                        family: 'Inter, sans-serif',
                        size: 11
                    },
                    color: '#64748b'
                },
                suggestedMax: maxValor
            }
        },
        plugins: {
            title: {
                display: false
            },
            legend: {
                position: 'top',
                align: 'center',
                labels: {
                    usePointStyle: true,
                    pointStyle: 'circle',
                    font: {
                        family: 'Inter, sans-serif',
                        size: 12,
                        weight: '500'
                    },
                    color: '#374151',
                    padding: 20
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true,
                titleFont: {
                    family: 'Inter, sans-serif',
                    size: 13,
                    weight: '600'
                },
                bodyFont: {
                    family: 'Inter, sans-serif',
                    size: 12
                },
                callbacks: {
                    label: function(context) {
                        const label = context.dataset.label || '';
                        const value = context.parsed.y;
                        const formattedValue = modoComparativo === 'quantidade'
                            ? value.toLocaleString('pt-BR')
                            : 'R$ ' + value.toLocaleString('pt-BR', {minimumFractionDigits: 2});
                        return label + ': ' + formattedValue;
                    }
                }
            }
        },
        elements: {
            line: {
                borderJoinStyle: 'round'
            }
        }
    }
});

// Inicializar tabela comparativa com valores
atualizarTabelaComparativo();

document.getElementById('filtroComparativo').addEventListener('change', function() {
    modoComparativo = this.value;
    if (modoComparativo === 'quantidade') {
        chartComp.data.datasets[0].data = dataEntradasComp;
        chartComp.data.datasets[1].data = dataSaidasComp;
        chartComp.options.scales.y.title.text = 'Quantidade';
        chartComp.options.scales.y.suggestedMax = maxValue;
        chartComp.options.plugins.title.text = 'Comparativo Entradas vs Saídas (Últimos 6 Meses)';
    } else {
        chartComp.data.datasets[0].data = dataValorEntradasComp;
        chartComp.data.datasets[1].data = dataValorSaidasComp;
        chartComp.options.scales.y.title.text = 'Valor (R$)';
        chartComp.options.scales.y.suggestedMax = maxValor;
        chartComp.options.plugins.title.text = 'Comparativo Entradas vs Saídas em Valor (Últimos 6 Meses)';
    }
    chartComp.update();
    atualizarTabelaComparativo();
});

function atualizarTabelaComparativo() {
    const modo = modoComparativo;
    document.querySelectorAll('#comparativo-tabela tbody tr').forEach((tr, idx) => {
        const entradas = modo === 'quantidade' ? dataEntradasComp[idx] : dataValorEntradasComp[idx].toLocaleString('pt-BR', {minimumFractionDigits: 2});
        const saidas = modo === 'quantidade' ? dataSaidasComp[idx] : dataValorSaidasComp[idx].toLocaleString('pt-BR', {minimumFractionDigits: 2});
        const diferenca = modo === 'quantidade' ? (dataEntradasComp[idx] - dataSaidasComp[idx]) : (dataValorEntradasComp[idx] - dataValorSaidasComp[idx]);
        tr.children[1].textContent = entradas;
        tr.children[2].textContent = saidas;
        tr.children[3].textContent = modo === 'quantidade' ? diferenca : diferenca.toLocaleString('pt-BR', {minimumFractionDigits: 2});
        tr.children[3].className = diferenca >= 0 ? 'text-success' : 'text-danger';
    });
}

}); // Fim do DOMContentLoaded para gráfico comparativo

// Gráfico de barras para Valor Total de Produtos por CNPJ
const ctxValorCNPJ = document.getElementById('valorCNPJChart').getContext('2d');
const valorCNPJChart = new Chart(ctxValorCNPJ, {
  type: 'bar',
  data: {
    labels: <?php echo json_encode($labelsValorCNPJ); ?>,
    datasets: [{
      label: 'Valor Total',
      data: <?php echo json_encode($dataValorCNPJ); ?>,
      backgroundColor: <?php echo json_encode($coresValorCNPJ); ?>,
      borderWidth: 1
    }]
  },
  options: {
    indexAxis: 'y',
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Valor Total (R$)'
        },
        ticks: {
          precision: 0
        },
        suggestedMax: <?php echo max($dataValorCNPJ) ? max($dataValorCNPJ)*1.1 : 100; ?>
      },
      y: {
        title: {
          display: true,
          text: 'Empresa'
        }
      }
    },
    plugins: {
      legend: {
        position: 'top'
      }
    }
  }
});

// Popup de seleção de empresa
function abrirPopupEmpresas() {
  document.getElementById('popupEmpresasCNPJ').style.display = 'flex';
  document.getElementById('filtroEmpresaCNPJ').value = '';
  filtrarEmpresasCNPJ();
}
function fecharPopupEmpresas() {
  document.getElementById('popupEmpresasCNPJ').style.display = 'none';
}
function filtrarEmpresasCNPJ() {
  const filtro = document.getElementById('filtroEmpresaCNPJ').value.toLowerCase();
  const linhas = document.querySelectorAll('#listaEmpresasCNPJ tr');
  linhas.forEach(tr => {
    const nome = tr.children[0].textContent.toLowerCase();
    const cnpj = tr.children[1].textContent.toLowerCase();
    tr.style.display = nome.includes(filtro) || cnpj.includes(filtro) ? '' : 'none';
  });
}
function selecionarEmpresaCNPJ(cnpj) {
  const url = new URL(window.location.href);
  url.searchParams.set('empresa_cnpj', cnpj);
  window.location.href = url.toString();
}

// Gráfico de pizza para Gastos por Setor
window.addEventListener('DOMContentLoaded', function() {
  const ctxGastosSetor = document.getElementById('gastosSetorChart').getContext('2d');
  new Chart(ctxGastosSetor, {
    type: 'doughnut',
    data: {
      labels: <?php echo json_encode($labelsSetor); ?>,
      datasets: [{
        data: <?php echo json_encode($dataValorSetor); ?>,
        backgroundColor: window.modernChartConfig.palette,
        borderColor: '#ffffff',
        borderWidth: 2,
        hoverBorderWidth: 3
      }]
    },
    options: {
      ...window.modernChartConfig.options.pie,
      cutout: '60%'
    }
  });
});

window.addEventListener('DOMContentLoaded', function() {
  const ctxValorContrato = document.getElementById('valorContratoChart').getContext('2d');
  new Chart(ctxValorContrato, {
    type: 'bar',
    data: {
      labels: <?php echo json_encode($labelsContrato); ?>,
      datasets: [{
        label: 'Valor Total',
        data: <?php echo json_encode($dataValorContrato); ?>,
        backgroundColor: window.modernChartConfig.colors.info,
        borderColor: window.modernChartConfig.colors.info,
        borderWidth: 0,
        borderRadius: 6,
        borderSkipped: false
      }]
    },
    options: {
      ...window.modernChartConfig.options.bar,
      indexAxis: 'y',
      plugins: {
        ...window.modernChartConfig.options.bar.plugins,
        legend: { display: false }
      },
      scales: {
        ...window.modernChartConfig.options.bar.scales,
        x: {
          ...window.modernChartConfig.options.bar.scales.x,
          beginAtZero: true,
          suggestedMax: Math.max(...<?php echo json_encode($dataValorContrato); ?>, 100) * 1.1
        }
      }
    }
  });
});

function verDetalhesContrato(contrato, mesFiltro) {
  const popup = document.getElementById('popupDetalhesContrato');
  const titulo = document.getElementById('detalhesContratoTitulo');
  const conteudo = document.getElementById('detalhesContratoConteudo');

  titulo.innerText = `Detalhes do Contrato: ${contrato}`;
  conteudo.innerHTML = '<p>Carregando...</p>';
  popup.style.display = 'flex';

  fetch(`obter-detalhes-contrato.php?contrato=${encodeURIComponent(contrato)}&mes_filtro=${encodeURIComponent(mesFiltro)}`)
    .then(response => response.json())
    .then(data => {
      if (data.error) {
        conteudo.innerHTML = `<p class="text-danger">${data.error}</p>`;
        return;
      }

      let html = '<table id="tabelaDetalhesContrato" class="table table-striped table-sm">';
      html += `<thead><tr><th>Produto</th><th>Quantidade</th><th>Valor Unit.</th><th>Valor Total</th><th>Origem</th><th>Data</th><th>ID Origem</th></tr></thead><tbody>`;

      if (data.length === 0) {
        html += '<tr><td colspan="7" class="text-center">Nenhum item encontrado para este contrato no período selecionado.</td></tr></tbody>';
      } else {
        let totalQuantidade = 0;
        let totalValor = 0.0;

        data.forEach(item => {
            const quantidade = parseInt(item.quantidade, 10) || 0;
            const valorTotal = parseFloat(item.valor_total) || 0.0;
            totalQuantidade += quantidade;
            totalValor += valorTotal;

          // Converter data para formato brasileiro
          let dataBR = '';
          if (item.data) {
            const d = new Date(item.data);
            if (!isNaN(d)) {
              dataBR = d.toLocaleDateString('pt-BR');
            } else if (/^\d{4}-\d{2}-\d{2}/.test(item.data)) {
              // fallback para datas tipo '2024-06-01'
              const [y, m, day] = item.data.split(' ')[0].split('-');
              dataBR = `${day}/${m}/${y}`;
            } else {
              dataBR = item.data;
            }
          }
          // NÃO adicionar apóstrofo, apenas manter o formato brasileiro
          // dataBR = "'" + dataBR;
          html += `
            <tr>
              <td>${item.produto}</td>
              <td>${quantidade}</td>
              <td>R$ ${parseFloat(item.valor_unitario).toFixed(2).replace('.', ',')}</td>
              <td>R$ ${valorTotal.toFixed(2).replace('.', ',')}</td>
              <td>${item.origem}</td>
              <td>${dataBR}</td>
              <td>${item.origem_id}</td>
            </tr>
          `;
        });
        html += '</tbody>'; // Close body
        html += `
          <tfoot style="font-weight: bold; background-color: #f2f2f2;">
            <tr>
              <td>TOTAIS</td>
              <td>${totalQuantidade}</td>
              <td></td>
              <td>R$ ${totalValor.toFixed(2).replace('.', ',')}</td>
              <td colspan="3"></td>
            </tr>
          </tfoot>
        `;
      }
      html += '</table>';
      conteudo.innerHTML = html;
    })
    .catch(error => {
      console.error('Erro ao buscar detalhes do contrato:', error);
      conteudo.innerHTML = '<p class="text-danger">Ocorreu um erro ao carregar os detalhes. Tente novamente.</p>';
    });
}

function fecharDetalhesContrato() {
  document.getElementById('popupDetalhesContrato').style.display = 'none';
}

function exportarDetalhesContrato() {
  const titulo = document.getElementById('detalhesContratoTitulo').innerText;
  const filename = titulo.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.xlsx';
  const table = document.getElementById('tabelaDetalhesContrato');
  const rows = Array.from(table.querySelectorAll('tr'));
  const data = rows.map(row => {
    return Array.from(row.querySelectorAll('th,td')).map((cell, idx) => {
      // Forçar a coluna de data (índice 5) a ser string
      if (idx === 5) return cell.textContent.trim();
      return cell.textContent.trim();
    });
  });
  const ws = XLSX.utils.aoa_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "Detalhes");
  XLSX.writeFile(wb, filename);
}

function imprimirDetalhesContrato() {
  const titulo = document.getElementById('detalhesContratoTitulo').outerHTML;
  const printContents = document.getElementById('tabelaDetalhesContrato').outerHTML;
  const originalContents = document.body.innerHTML;
  document.body.innerHTML = `<html><head><title>Imprimir Detalhes</title><link rel="stylesheet" href="style.css"><style>body{padding:1rem;} table{font-size: 12px;}</style></head><body>${titulo}${printContents}</body></html>`;
  window.print();
  document.body.innerHTML = originalContents;
  location.reload(); // Recarrega a página para restaurar scripts e o estado original
}

function verDetalhesSetor(setor) {
  const popup = document.getElementById('popupDetalhesSetor');
  const titulo = document.getElementById('detalhesSetorTitulo');
  const conteudo = document.getElementById('detalhesSetorConteudo');

  titulo.innerText = `Detalhes do Setor: ${setor}`;
  conteudo.innerHTML = '<p>Carregando...</p>';
  popup.style.display = 'flex';

  fetch(`obter-detalhes-setor.php?setor=${encodeURIComponent(setor)}`)
    .then(response => response.json())
    .then(data => {
      if (data.error) {
        conteudo.innerHTML = `<p class="text-danger">${data.error}</p>`;
        return;
      }

      let html = '<table id="tabelaDetalhesSetor" class="table table-striped table-sm" style="font-size: 0.85rem;">';
      html += `<thead><tr><th>Produto</th><th>Destinatário</th><th>Qtd</th><th>Vlr. Unit.</th><th>Vlr. Total</th><th>Origem</th><th>Data</th><th>ID Origem</th></tr></thead><tbody>`;

      if (data.length === 0) {
        html += '<tr><td colspan="8" class="text-center">Nenhum item encontrado para este setor.</td></tr></tbody>';
      } else {
        let totalQuantidade = 0;
        let totalValor = 0.0;

        data.forEach(item => {
          const quantidade = parseInt(item.quantidade, 10) || 0;
          const valorTotal = parseFloat(item.valor_total) || 0.0;
          totalQuantidade += quantidade;
          totalValor += valorTotal;

          let dataBR = '';
          if (item.data) {
              const d = new Date(item.data);
              const timezoneOffset = d.getTimezoneOffset() * 60000;
              const adjustedDate = new Date(d.getTime() + timezoneOffset);
              if (!isNaN(adjustedDate)) {
                  dataBR = adjustedDate.toLocaleDateString('pt-BR');
              } else {
                  dataBR = item.data;
              }
          }

          html += `
            <tr>
              <td>${item.produto || ''}</td>
              <td>${item.destinatario || ''}</td>
              <td>${quantidade}</td>
              <td>R$ ${parseFloat(item.valor_unitario).toFixed(2).replace('.', ',')}</td>
              <td>R$ ${valorTotal.toFixed(2).replace('.', ',')}</td>
              <td>${item.origem}</td>
              <td>${dataBR}</td>
              <td>${item.origem_id}</td>
            </tr>
          `;
        });
        html += '</tbody>';
        html += `
          <tfoot style="font-weight: bold; background-color: #f2f2f2;">
            <tr>
              <td colspan="2">TOTAIS</td>
              <td>${totalQuantidade}</td>
              <td></td>
              <td>R$ ${totalValor.toFixed(2).replace('.', ',')}</td>
              <td colspan="3"></td>
            </tr>
          </tfoot>
        `;
      }
      html += '</table>';
      conteudo.innerHTML = html;
    })
    .catch(error => {
      console.error('Erro ao buscar detalhes do setor:', error);
      conteudo.innerHTML = '<p class="text-danger">Ocorreu um erro ao carregar os detalhes. Tente novamente.</p>';
    });
}

function fecharDetalhesSetor() {
  document.getElementById('popupDetalhesSetor').style.display = 'none';
}

function exportarDetalhesSetor() {
    const titulo = document.getElementById('detalhesSetorTitulo').innerText;
    const filename = 'detalhes_' + titulo.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.xlsx';
    const table = document.getElementById('tabelaDetalhesSetor');
    const rows = Array.from(table.querySelectorAll('tr'));
    const data = rows.map(row => {
        return Array.from(row.querySelectorAll('th,td')).map(cell => cell.textContent.trim());
    });
    const ws = XLSX.utils.aoa_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Detalhes");
    XLSX.writeFile(wb, filename);
}

function imprimirDetalhesSetor() {
    const titulo = document.getElementById('detalhesSetorTitulo').outerHTML;
    const printContents = document.getElementById('tabelaDetalhesSetor').outerHTML;
    const originalContents = document.body.innerHTML;
    document.body.innerHTML = `<html><head><title>Imprimir Detalhes</title><link rel="stylesheet" href="style.css"><style>body{padding:1rem;} table{font-size: 12px;}</style></head><body>${titulo}${printContents}</body></html>`;
    window.print();
    document.body.innerHTML = originalContents;
    location.reload();
}

function verDetalhesCNPJ(cnpj, nomeEmpresa, mesFiltro) {
    const popup = document.getElementById('popupDetalhesCNPJ');
    const titulo = document.getElementById('detalhesCNPJTitulo');
    const conteudo = document.getElementById('detalhesCNPJConteudo');

    titulo.innerText = `Detalhes de: ${nomeEmpresa} (${cnpj})`;
    conteudo.innerHTML = '<p>Carregando...</p>';
    popup.style.display = 'flex';

    fetch(`obter-detalhes-cnpj.php?cnpj=${encodeURIComponent(cnpj)}&mes_filtro=${encodeURIComponent(mesFiltro)}`)
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            conteudo.innerHTML = `<p class="text-danger">${data.error}</p>`;
            return;
        }

        let html = '<table id="tabelaDetalhesCNPJ" class="table table-striped table-sm" style="font-size: 0.85rem;">';
        html += `<thead><tr><th>Produto</th><th>Destinatário</th><th>Qtd</th><th>Vlr. Unit.</th><th>Vlr. Total</th><th>Origem</th><th>Data</th><th>ID Origem</th></tr></thead><tbody>`;

        if (data.length === 0) {
            html += '<tr><td colspan="8" class="text-center">Nenhum item encontrado para este CNPJ no período.</td></tr></tbody>';
        } else {
            let totalQuantidade = 0;
            let totalValor = 0.0;

            data.forEach(item => {
                const quantidade = parseInt(item.quantidade, 10) || 0;
                const valorTotal = parseFloat(item.valor_total) || 0.0;
                totalQuantidade += quantidade;
                totalValor += valorTotal;

                let dataBR = '';
                if (item.data) {
                    const d = new Date(item.data);
                    const timezoneOffset = d.getTimezoneOffset() * 60000;
                    const adjustedDate = new Date(d.getTime() + timezoneOffset);
                    dataBR = !isNaN(adjustedDate) ? adjustedDate.toLocaleDateString('pt-BR') : item.data;
                }

                html += `
                <tr>
                  <td>${item.produto || ''}</td>
                  <td>${item.destinatario || ''}</td>
                  <td>${quantidade}</td>
                  <td>R$ ${parseFloat(item.valor_unitario).toFixed(2).replace('.', ',')}</td>
                  <td>R$ ${valorTotal.toFixed(2).replace('.', ',')}</td>
                  <td>${item.origem}</td>
                  <td>${dataBR}</td>
                  <td>${item.origem_id}</td>
                </tr>
                `;
            });
            html += '</tbody>';
            html += `
            <tfoot style="font-weight: bold; background-color: #f2f2f2;">
                <tr>
                <td colspan="2">TOTAIS</td>
                <td>${totalQuantidade}</td>
                <td></td>
                <td>R$ ${totalValor.toFixed(2).replace('.', ',')}</td>
                <td colspan="3"></td>
                </tr>
            </tfoot>
            `;
        }
        html += '</table>';
        conteudo.innerHTML = html;
    })
    .catch(error => {
        console.error('Erro ao buscar detalhes do CNPJ:', error);
        conteudo.innerHTML = '<p class="text-danger">Ocorreu um erro ao carregar os detalhes.</p>';
    });
}

function fecharDetalhesCNPJ() {
    document.getElementById('popupDetalhesCNPJ').style.display = 'none';
}

function exportarDetalhesCNPJ() {
    const titulo = document.getElementById('detalhesCNPJTitulo').innerText;
    const filename = 'detalhes_' + titulo.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.xlsx';
    const table = document.getElementById('tabelaDetalhesCNPJ');
    const rows = Array.from(table.querySelectorAll('tr'));
    const data = rows.map(row => {
        return Array.from(row.querySelectorAll('th,td')).map(cell => cell.textContent.trim());
    });
    const ws = XLSX.utils.aoa_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Detalhes");
    XLSX.writeFile(wb, filename);
}

function imprimirDetalhesCNPJ() {
    const titulo = document.getElementById('detalhesCNPJTitulo').outerHTML;
    const printContents = document.getElementById('tabelaDetalhesCNPJ').outerHTML;
    const originalContents = document.body.innerHTML;
    document.body.innerHTML = `<html><head><title>Imprimir Detalhes</title><link rel="stylesheet" href="style.css"><style>body{padding:1rem;} table{font-size: 12px;}</style></head><body>${titulo}${printContents}</body></html>`;
    window.print();
    document.body.innerHTML = originalContents;
    location.reload();
}
</script>



<!-- Popup Detalhes do Contrato -->
<div id="popupDetalhesContrato" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.5); z-index:10000; align-items:center; justify-content:center;">
  <div style="background:#fff; border-radius:8px; padding:24px; width:90%; max-width:800px; max-height:90vh; overflow-y:auto; box-shadow:0 4px 16px rgba(0,0,0,0.2);">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4 id="detalhesContratoTitulo">Detalhes do Contrato</h4>
        <div>
            <button class="btn btn-success btn-sm me-1" title="Exportar XLSX" onclick="exportarDetalhesContrato()"><i class="fas fa-file-excel"></i></button>
            <button class="btn btn-danger btn-sm" title="Exportar PDF" onclick="imprimirDetalhesContrato()"><i class="fas fa-file-pdf"></i></button>
        </div>
    </div>
    <div id="detalhesContratoConteudo">
      <!-- AJAX content will be loaded here -->
    </div>
    <button class="btn btn-secondary mt-3" onclick="fecharDetalhesContrato()">Fechar</button>
  </div>
</div>
<!-- Popup Detalhes do Setor -->
<div id="popupDetalhesSetor" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.5); z-index:10000; align-items:center; justify-content:center;">
  <div style="background:#fff; border-radius:8px; padding:24px; width:90%; max-width:950px; max-height:90vh; overflow-y:auto; box-shadow:0 4px 16px rgba(0,0,0,0.2);">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4 id="detalhesSetorTitulo">Detalhes do Setor</h4>
        <div>
            <button class="btn btn-success btn-sm me-1" title="Exportar XLSX" onclick="exportarDetalhesSetor()"><i class="fas fa-file-excel"></i></button>
            <button class="btn btn-danger btn-sm" title="Exportar PDF" onclick="imprimirDetalhesSetor()"><i class="fas fa-file-pdf"></i></button>
        </div>
    </div>
    <div id="detalhesSetorConteudo">
      <!-- AJAX content will be loaded here -->
    </div>
    <button class="btn btn-secondary mt-3" onclick="fecharDetalhesSetor()">Fechar</button>
  </div>
</div>
<!-- Popup Detalhes do CNPJ -->
<div id="popupDetalhesCNPJ" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.5); z-index:10000; align-items:center; justify-content:center;">
  <div style="background:#fff; border-radius:8px; padding:24px; width:90%; max-width:950px; max-height:90vh; overflow-y:auto; box-shadow:0 4px 16px rgba(0,0,0,0.2);">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4 id="detalhesCNPJTitulo">Detalhes por CNPJ</h4>
        <div>
            <button class="btn btn-success btn-sm me-1" title="Exportar XLSX" onclick="exportarDetalhesCNPJ()"><i class="fas fa-file-excel"></i></button>
            <button class="btn btn-danger btn-sm" title="Exportar PDF" onclick="imprimirDetalhesCNPJ()"><i class="fas fa-file-pdf"></i></button>
        </div>
    </div>
    <div id="detalhesCNPJConteudo">
      <!-- AJAX content will be loaded here -->
    </div>
    <button class="btn btn-secondary mt-3" onclick="fecharDetalhesCNPJ()">Fechar</button>
  </div>
</div>

    <!-- Card: EPIs Próximos do Vencimento -->
    <div class="card-container-epi">
    <div class="modern-card card-epi">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-shield-alt"></i>
          EPIs Próximos do Vencimento
        </h3>
        <div class="action-buttons">
          <div class="export-dropdown">
            <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-epis')" title="Opções de exportação">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <div class="export-dropdown-content" id="dropdown-epis">
              <button class="export-dropdown-item excel" onclick="exportarTabela('epis-vencimento-tabela', 'epis_a_vencer.xlsx')">
                <i class="fas fa-file-excel"></i>
                Exportar Excel
              </button>
              <button class="export-dropdown-item pdf" onclick="imprimirTabela('epis-vencimento-tabela')">
                <i class="fas fa-file-pdf"></i>
                Exportar PDF
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="card-content">
        <!-- Filtros -->
        <form method="get" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 16px; margin-bottom: 24px; padding: 20px; background: var(--light-bg); border-radius: var(--radius-md);">
          <div>
            <label for="filtro_epi_nome" style="display: block; font-size: 12px; font-weight: 600; color: var(--text-primary); margin-bottom: 6px;">Funcionário</label>
            <input type="text" id="filtro_epi_nome" name="filtro_epi_nome"
                   style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: var(--radius-sm); font-size: 14px;"
                   value="<?php echo htmlspecialchars($filtro_epi_nome); ?>" placeholder="Nome do funcionário">
          </div>
          <div>
            <label for="filtro_epi_empresa" style="display: block; font-size: 12px; font-weight: 600; color: var(--text-primary); margin-bottom: 6px;">Empresa</label>
            <select id="filtro_epi_empresa" name="filtro_epi_empresa"
                    style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: var(--radius-sm); font-size: 14px;">
              <option value="">Todas</option>
              <?php foreach($empresas_epi as $emp): ?>
                <option value="<?php echo htmlspecialchars($emp['posto']); ?>" <?php if($filtro_epi_empresa == $emp['posto']) echo 'selected'; ?>><?php echo htmlspecialchars($emp['posto']); ?></option>
              <?php endforeach; ?>
            </select>
          </div>
          <div>
            <label for="filtro_epi_setor" style="display: block; font-size: 12px; font-weight: 600; color: var(--text-primary); margin-bottom: 6px;">Setor</label>
            <select id="filtro_epi_setor" name="filtro_epi_setor"
                    style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: var(--radius-sm); font-size: 14px;">
              <option value="">Todos</option>
              <?php foreach($setores_epi as $setor): ?>
                <option value="<?php echo htmlspecialchars($setor['setor']); ?>" <?php if($filtro_epi_setor == $setor['setor']) echo 'selected'; ?>><?php echo htmlspecialchars($setor['setor']); ?></option>
              <?php endforeach; ?>
            </select>
          </div>
          <div>
            <label for="filtro_epi_dias" style="display: block; font-size: 12px; font-weight: 600; color: var(--text-primary); margin-bottom: 6px;">Vence em até</label>
            <select id="filtro_epi_dias" name="filtro_epi_dias"
                    style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: var(--radius-sm); font-size: 14px;">
              <option value="15" <?php if($filtro_epi_dias == 15) echo 'selected'; ?>>15 dias</option>
              <option value="30" <?php if($filtro_epi_dias == 30) echo 'selected'; ?>>30 dias</option>
              <option value="60" <?php if($filtro_epi_dias == 60) echo 'selected'; ?>>60 dias</option>
              <option value="90" <?php if($filtro_epi_dias == 90) echo 'selected'; ?>>90 dias</option>
            </select>
          </div>
          <div style="display: flex; align-items: end; gap: 8px;">
            <button type="submit" class="btn-modern" style="background: var(--primary-color); color: white; border-color: var(--primary-color);">
              <i class="fas fa-filter"></i> Filtrar
            </button>
            <a href="index.php" class="btn-modern">
              <i class="fas fa-times"></i> Limpar
            </a>
          </div>
        </form>

        <!-- Tabela -->
        <div class="table-container" style="max-height: 500px; overflow-y: auto;">
          <table class="modern-table" id="epis-vencimento-tabela">
            <thead style="position: sticky; top: 0; background: var(--card-bg); z-index: 10;">
              <tr>
                <th>Funcionário</th>
                <th>EPI</th>
                <th>Função</th>
                <th>Setor</th>
                <th>Dias Restantes</th>
                <th>Data de Vencimento</th>
              </tr>
            </thead>
            <tbody>
              <?php if (empty($epis_a_vencer)): ?>
                <tr>
                  <td colspan="6" style="text-align: center; color: var(--text-secondary); padding: 40px;">
                    <i class="fas fa-shield-alt" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <p>Nenhum EPI próximo do vencimento com os filtros selecionados.</p>
                  </td>
                </tr>
              <?php else: ?>
                <?php foreach ($epis_a_vencer as $epi): ?>
                <tr>
                  <td><?php echo htmlspecialchars($epi['funcionario_nome']); ?></td>
                  <td><?php echo htmlspecialchars($epi['epi_nome']); ?></td>
                  <td><?php echo htmlspecialchars($epi['funcao']); ?></td>
                  <td><?php echo htmlspecialchars($epi['setor']); ?></td>
                  <td>
                    <?php if ($epi['dias_restantes'] <= 7): ?>
                      <span class="status-badge danger"><?php echo $epi['dias_restantes']; ?> dias</span>
                    <?php elseif ($epi['dias_restantes'] <= 15): ?>
                      <span class="status-badge warning"><?php echo $epi['dias_restantes']; ?> dias</span>
                    <?php else: ?>
                      <span class="status-badge info"><?php echo $epi['dias_restantes']; ?> dias</span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php
                      $dataEntrega = new DateTime($epi['data_entrega']);
                      $dataEntrega->add(new DateInterval('P' . $epi['validade_uso'] . 'M'));
                      echo $dataEntrega->format('d/m/Y');
                    ?>
                  </td>
                </tr>
                <?php endforeach; ?>
              <?php endif; ?>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    </div> <!-- Fim do card-container-epi -->


  </div> <!-- Fim da área de cards independentes -->

  </div> <!-- Fim do dashboard-grid -->

</div> <!-- Fim do content-container -->

<script>
// Tornar card de solicitações clicável
document.addEventListener('DOMContentLoaded', function() {
  const cardSolicitacoes = document.querySelector('.card-solicitacoes');
  if (cardSolicitacoes) {
    cardSolicitacoes.addEventListener('click', function() {
      window.location.href = 'todas-solitacoes-estoque.php';
    });
  }
});
</script>

</body>
</html>

